ALTER TABLE `proj_fitness_workout_image`
    ADD COLUMN `workout_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '手组 workout id 集合' AFTER `detail_image`;
ALTER TABLE `proj_fitness_workout_image_pub`
    ADD COLUMN `workout_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '手组 workout id 集合' AFTER `detail_image`;

-- 为 proj_fitness_workout_generate 表添加新字段
ALTER TABLE `proj_fitness_workout_generate`
    ADD COLUMN `abs_rate` DECIMAL(5,2) NULL COMMENT 'Main中Abs占Main的比例，保留2位小数' AFTER `calorie`;
ALTER TABLE `proj_fitness_workout_generate`
    ADD COLUMN `standing_rate` DECIMAL(5,2) NULL COMMENT 'Main中Standing占Main的比例，保留2位小数' AFTER `abs_rate`;

