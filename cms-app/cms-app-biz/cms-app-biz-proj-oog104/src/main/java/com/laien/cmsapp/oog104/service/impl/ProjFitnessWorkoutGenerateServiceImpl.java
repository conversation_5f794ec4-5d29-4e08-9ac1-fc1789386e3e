/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.cmsapp.oog104.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.*;
import com.laien.cmsapp.oog104.mapper.*;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessExerciseVideoMapStruct;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessWorkoutGenerateMapStruct;
import com.laien.cmsapp.oog104.request.DailyHabitReq;
import com.laien.cmsapp.oog104.request.FitnessWorkoutGeneratePlanReq;
import com.laien.cmsapp.oog104.request.PlanWorkoutRefreshReq;
import com.laien.cmsapp.oog104.response.*;
import com.laien.cmsapp.oog104.service.*;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import com.laien.common.oog104.enums.DailyTypeMappingEnums;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.common.oog104.enums.manual.*;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.mybatisplus.config.BaseModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.common.oog104.enums.template.TemplateTypeEnums.REGULAR_FITNESS;

/**
 * <p>TODO </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjFitnessWorkoutGenerateServiceImpl extends ServiceImpl<ProjFitnessWorkoutGenerateMapper, ProjFitnessWorkoutGenerate>
        implements IProjFitnessWorkoutGenerateService {

    private final ProjFitnessExerciseVideoMapper exerciseVideoMapper;

    private final ProjFitnessManualWorkoutPubMapper fitnessManualWorkoutPubMapper;

    private final ProjFitnessWorkoutGenerateExerciseVideoMapper projFitnessWorkoutGenerateExerciseVideoMapper;

    private final ProjFitnessManualWorkoutExerciseVideoPubMapper projFitnessManualWorkoutExerciseVideoPubMapper;

    private final ProjFitnessExerciseVideoMapStruct exerciseVideoMapStruct;

    private final ProjFitnessWorkoutGenerateMapStruct projFitnessWorkoutGenerateMapStruct;

    private final ProjFitnessTemplateExerciseGroupMapper projFitnessTemplateExerciseGroupMapper;

    private final ProjFitnessWorkoutImagePubService projFitnessWorkoutImagePubService;

    private final ProjFitnessTemplatePubService projFitnessTemplatePubService;

    private final IProjFitnessWorkoutGenerateI18nService i18nService;

    private final IProjFitnessManualWorkoutI18nPubService i18nPubService;

    private final IProjFitnessExerciseVideoService videoService;

    private final ICoreTextTaskI18nPubService textTaskI18nPubService;

    private final IProjLmsI18nService projLmsI18nService;



    @Override
    public List<ProjFitnessGenerateWorkoutDetailVO> detailList(List<PlanWorkoutRefreshReq> req) {
        // 按table code 区分查询不同的表
        Map<Integer, List<PlanWorkoutRefreshReq>> tableCodeEnumsListMap = req.stream().collect(Collectors.groupingBy(PlanWorkoutRefreshReq::getCode));
        return tableCodeEnumsListMap.values().stream().map(list -> {
            TableCodeEnums tableCodeEnums = TableCodeEnums.get(list.get(0).getCode());
            if (Objects.equals(TableCodeEnums.PROJ_FITNESS_WORKOUT_GENERATE, tableCodeEnums)) {
                Map<Integer,Integer> idMap = list.stream().collect(Collectors.toMap(PlanWorkoutRefreshReq::getId,PlanWorkoutRefreshReq::getImgId));
                return this.selectDetailsByIdMap(idMap);
            }
            if(Objects.equals(TableCodeEnums.PROJ_FITNESS_MANUAL_WORKOUT, tableCodeEnums)) {
                Map<Integer,Integer> idMap = list.stream().collect(Collectors.toMap(PlanWorkoutRefreshReq::getId,PlanWorkoutRefreshReq::getImgId));
                return this.selectManualDetailsByIdMap(idMap);
            }
            log.warn("table code is not support");
            return new ArrayList<ProjFitnessGenerateWorkoutDetailVO>();
        }).flatMap(List::stream).collect(Collectors.toList());
    }

    @Override
    public ProjFitnessGenerateWorkoutPlanVO plan(FitnessWorkoutGeneratePlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        ProjFitnessGenerateWorkoutPlanVO plan = new ProjFitnessGenerateWorkoutPlanVO();
        List<ProjFitnessGenerateWorkoutDetailVO> workoutDetailList = new ArrayList<>();
        plan.setWorkoutDetailList(workoutDetailList);
        //判断 template type 是否为空,兜底默认 regular fitness
        if(Objects.isNull(planReq.getTemplateType()))
            planReq.setTemplateType(REGULAR_FITNESS);

        List<ProjFitnessWorkoutImagePub> imageList = projFitnessWorkoutImagePubService.match(planReq, versionInfoBO);
        if (CollUtil.isEmpty(imageList)) {
            log.error("fitness generate workout plan imageList is empty, planReq:{}", planReq);
            return plan;
        }
       // projLmsI18nService.handleTextI18n(imageList, ProjCodeEnums.OOG104);
        List<ProjFitnessTemplatePub> templateList = projFitnessTemplatePubService.list(planReq, versionInfoBO);
        if (CollUtil.isEmpty(templateList)) {
            log.error("fitness generate workout plan template list is empty, planReq:{}", planReq);
            return plan;
        }
        Set<Integer> templateIdSet = templateList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        List<ProjFitnessWorkoutGenerate> workoutList = list(planReq, templateIdSet);
        if (CollUtil.isEmpty(workoutList)) {
            log.error("fitness generate workout list is empty, planReq:{}", planReq);
            return plan;
        }
        workoutDetailList = match(planReq, templateIdSet, imageList);
        Map<Integer, ProjFitnessGenerateWorkoutDetailVO> imageWorkoutMap = workoutDetailList.stream()
                .collect(Collectors.toMap(ProjFitnessGenerateWorkoutDetailVO::getImgId, Function.identity()));
        //判断用户类型,当为新用户时,固定前三天手组 workout
        List<Integer> fixedWorkoutIds = new ArrayList<>();
        if(Objects.equals(FitnessUserTypeEnums.NEW_USER, planReq.getUserType())) {
        //TODO nacos 配置固定手组 workout ids
            // 查询固定 ID 的手组
            LambdaQueryWrapper<ProjFitnessManualWorkoutPub> fixedManualWorkoutQu =   new LambdaQueryWrapper<ProjFitnessManualWorkoutPub>()
                    .in(ProjFitnessManualWorkoutPub::getId, fixedWorkoutIds)
                    .eq(ProjFitnessManualWorkoutPub::getVersion, versionInfoBO.getCurrentVersion())
                    .eq(ProjFitnessManualWorkoutPub::getDelFlag, GlobalConstant.NO)
                    .orderByAsc(ProjFitnessManualWorkoutPub::getId);
            List<ProjFitnessManualWorkoutPub> fixedManualWorkoutList = fitnessManualWorkoutPubMapper.selectList(fixedManualWorkoutQu);
            //开始替换前 3 天
            for(int i = 0;i < 3;i++) {
                generateManualWorkoutInfo(versionInfoBO, workoutDetailList.get(i), fixedManualWorkoutList.remove(0), imageWorkoutMap);
            }
        }



        // 第3项后加入列表再打乱
        List<ProjFitnessGenerateWorkoutDetailVO> replaceVoList = new ArrayList<>(workoutDetailList.subList(3, workoutDetailList.size()));
        Collections.shuffle(replaceVoList);

        // 处理手组限制
        Set<ExerciseVideoSpecialLimitEnums> specialLimitSet = planReq.getSpecialLimitSet();
        ExclusiveTypeEnums exclusiveType = planReq.getExclusiveType();
        if (exclusiveType == ExclusiveTypeEnums.POSTPARTUM) {
            planReq.setDifficulty(ManualDifficultyEnums.BEGINNER);
        } else if (exclusiveType == ExclusiveTypeEnums.INJURY) {
            planReq.setDifficulty(ManualDifficultyEnums.NEWBIE);
        }
        if (specialLimitSet == null) specialLimitSet = new HashSet<>();
        if (exclusiveType == ExclusiveTypeEnums.PREGNANT || exclusiveType == ExclusiveTypeEnums.POSTPARTUM) {
            specialLimitSet.add(ExerciseVideoSpecialLimitEnums.ABS);
        }
        Set<ExerciseVideoSpecialLimitEnums> finalSpecialLimitSet = specialLimitSet;

        // 查手组
        List<ProjFitnessManualWorkoutPub> fitnessManualWorkoutPubs = fitnessManualWorkoutPubMapper.getManualWorkoutByType(
                planReq,
                planReq.getDurationRange().getMin(),
                planReq.getDurationRange().getMax(),
                versionInfoBO.getCurrentVersion(),fixedWorkoutIds
        );

        if (CollUtil.isNotEmpty(finalSpecialLimitSet)) {
            fitnessManualWorkoutPubs = fitnessManualWorkoutPubs.stream()
                    .filter(w -> CollUtil.containsAll(w.getSpecialLimit(), finalSpecialLimitSet))
                    .collect(Collectors.toList());
        }

        // 手组按 target 分组
        Map<ManualTargetEnums, List<ProjFitnessManualWorkoutPub>> targetManualWorkoutsMap = fitnessManualWorkoutPubs.stream()
                .flatMap(w -> w.getTarget().stream().map(t -> new AbstractMap.SimpleEntry<>(t, w)))
                .collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.mapping(Map.Entry::getValue, Collectors.toList())));

        Map<Integer, ProjFitnessWorkoutImagePub> imageIdMap = imageList.stream()
                .collect(Collectors.toMap(ProjFitnessWorkoutImagePub::getId, Function.identity()));

        int manualNum = 0;

        for (ProjFitnessGenerateWorkoutDetailVO replaceWork : replaceVoList) {
            if (manualNum >= 3) break;

            ProjFitnessWorkoutImagePub replaceImage = imageIdMap.get(replaceWork.getImgId());
            if (replaceImage == null) continue;

            List<ProjFitnessManualWorkoutPub> manualList = targetManualWorkoutsMap.getOrDefault(replaceImage.getTarget(), Collections.emptyList());
            if (CollUtil.isEmpty(manualList)) continue;

            ProjFitnessManualWorkoutPub manual = manualList.remove(0);
            generateManualWorkoutInfo(versionInfoBO, replaceWork, manual, imageWorkoutMap);
            manualNum++;
        }
        plan.setWorkoutDetailList(workoutDetailList);

        projLmsI18nService.handleTextI18n(workoutDetailList, ProjCodeEnums.OOG104);
        return plan;
    }

    private void generateManualWorkoutInfo(ProjPublishCurrentVersionInfoBO versionInfoBO, ProjFitnessGenerateWorkoutDetailVO replaceWork, ProjFitnessManualWorkoutPub manual, Map<Integer, ProjFitnessGenerateWorkoutDetailVO> imageWorkoutMap) {
        generateManualWorkout(versionInfoBO, replaceWork, manual);
        // 查绑定 image
        List<ProjFitnessWorkoutImagePub> bindImageList = projFitnessWorkoutImagePubService.list(
                new LambdaQueryWrapper<ProjFitnessWorkoutImagePub>()
                        .eq(ProjFitnessWorkoutImagePub::getVersion, versionInfoBO.getCurrentVersion())
                        .apply("FIND_IN_SET({0}, workout_ids) > 0", manual.getId())
        );

        for (int i = 0; i < bindImageList.size(); i++) {
            ProjFitnessWorkoutImagePub bindImage = bindImageList.get(i);
            ProjFitnessGenerateWorkoutDetailVO mapped = imageWorkoutMap.get(bindImage.getId());

            if (mapped == null) {
                replaceWork.setImgId(bindImage.getId());
                replaceWork.setCoverImage(bindImage.getCoverImage());
                replaceWork.setDetailImage(bindImage.getDetailImage());
                break;
            } else if (Objects.equals(mapped.getImgId(), bindImage.getId())) {
                break;
            } else if (i == bindImageList.size() - 1) {
                // 最后一个绑定图，进行交换
                mapped.setImgId(replaceWork.getId());
                mapped.setCoverImage(replaceWork.getCoverImage());
                mapped.setDetailImage(replaceWork.getDetailImage());

                replaceWork.setImgId(bindImage.getId());
                replaceWork.setCoverImage(bindImage.getCoverImage());
                replaceWork.setDetailImage(bindImage.getDetailImage());
                break;
            }
        }
    }

    @Override
    public List<ProjFitnessDailyHabitWorkoutDetailVO> dailyHabitList(DailyHabitReq req, ProjPublishCurrentVersionInfoBO versionInfoBO, ExclusiveTypeEnums exclusiveType) {

        DailyTypeMappingEnums dailyTypeMapping = DailyTypeMappingEnums.get(exclusiveType);
        List<ProjFitnessWorkoutImagePub> imageList = projFitnessWorkoutImagePubService.match(req,dailyTypeMapping , versionInfoBO, exclusiveType);
        if(CollUtil.isEmpty(imageList)){
            log.error("daily habit image is empty, req:{}", req);
            return new ArrayList<>();
        }
        projLmsI18nService.handleTextI18n(imageList, ProjCodeEnums.OOG104);

        List<ProjFitnessTemplatePub> templateList = projFitnessTemplatePubService.list(req, dailyTypeMapping, REGULAR_FITNESS, versionInfoBO);
        if (CollUtil.isEmpty(templateList)) {
            log.error("daily habit template list is empty, req:{}", req);
            return new ArrayList<>();
        }
        Set<Integer> templateIdSet = templateList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        List<ProjFitnessWorkoutGenerate> workoutList = match(templateIdSet, req, dailyTypeMapping);
        if(CollUtil.isEmpty(workoutList)){
            log.error("daily habit workout is empty, req:{}", req);
            return new ArrayList<>();
        }
        Collections.shuffle(workoutList);
        List<ProjFitnessGenerateWorkoutDetailVO> generateWorkoutList = toGenerateWorkoutDetailVO(imageList, workoutList);
        List<ProjFitnessDailyHabitWorkoutDetailVO> dailyHabitWorkoutList = new ArrayList<>();
        if (CollUtil.isEmpty(generateWorkoutList)) {
            return dailyHabitWorkoutList;
        }
        for (ProjFitnessGenerateWorkoutDetailVO workout : generateWorkoutList) {
            ProjFitnessDailyHabitWorkoutDetailVO dailyHabitWorkout = new ProjFitnessDailyHabitWorkoutDetailVO();
            BeanUtils.copyProperties(workout, dailyHabitWorkout);
            dailyHabitWorkout.setExclusiveType(exclusiveType);
            dailyHabitWorkoutList.add(dailyHabitWorkout);
        }
        return dailyHabitWorkoutList;
    }

    private List<ProjFitnessWorkoutGenerate> match(Set<Integer> templateIdSet, DailyHabitReq req, DailyTypeMappingEnums dailyTypeMapping) {
        List<ManualDifficultyEnums> difficultyList = dailyTypeMapping.getDifficultyList();
        if(CollUtil.isEmpty(difficultyList)){
            difficultyList = Collections.singletonList(req.getDifficulty());
        }
        Set<ExerciseVideoSpecialLimitEnums> specialLimitSet = req.getSpecialLimitSet();
        LambdaQueryWrapper<ProjFitnessWorkoutGenerate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessWorkoutGenerate::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessWorkoutGenerate::getDifficulty, difficultyList)
                .eq(ProjFitnessWorkoutGenerate::getTarget, dailyTypeMapping.getTarget())
                .in(ProjFitnessWorkoutGenerate::getProjFitnessTemplateId, templateIdSet);
        List<ProjFitnessWorkoutGenerate> workoutList = baseMapper.selectList(wrapper);
        if(CollUtil.isEmpty(workoutList)){
            return workoutList;
        }

        return workoutList.stream()
                .filter(item -> CollUtil.containsAll(item.getSpecialLimit(), specialLimitSet) || CollUtil.isEmpty(item.getSpecialLimit()))
                .collect(Collectors.toList());
    }

    private List<ProjFitnessGenerateWorkoutDetailVO> match(FitnessWorkoutGeneratePlanReq planReq, Set<Integer> templateIdSet,List<ProjFitnessWorkoutImagePub> imageList) {
        List<ProjFitnessWorkoutGenerate> workoutList = list(planReq, templateIdSet);
        if(CollUtil.isEmpty(workoutList)){
            return new ArrayList<>();
        }
        Collections.shuffle(workoutList);
        return toGenerateWorkoutDetailVO(imageList, workoutList);
    }

    private List<ProjFitnessGenerateWorkoutDetailVO> toGenerateWorkoutDetailVO(List<ProjFitnessWorkoutImagePub> imageList, List<ProjFitnessWorkoutGenerate> workoutList) {
        Map<ManualTargetEnums, List<ProjFitnessWorkoutGenerate>> workoutTargetGroup = workoutList.stream().collect(Collectors.groupingBy(ProjFitnessWorkoutGenerate::getTarget));

        List<ProjFitnessWorkoutGenerate> matchedWorkoutList = new ArrayList<>();
        for (ProjFitnessWorkoutImagePub image : imageList) {
            List<ProjFitnessWorkoutGenerate> workoutGenerateList = workoutTargetGroup.getOrDefault(image.getTarget(), Collections.emptyList());
            if(CollUtil.isNotEmpty(workoutGenerateList)){
                ProjFitnessWorkoutGenerate workout = workoutGenerateList.remove(0);
                workout.setProjFitnessWorkoutImageId(image.getId());
                matchedWorkoutList.add(workout);
            }
        }
        Map<Integer, ProjFitnessWorkoutImagePub> imageIdGroup = imageList.stream()
                .collect(
                        Collectors.toMap(BaseModel::getId, Function.identity(),
                                (oldValue,
                                 newValue) -> newValue)
                );
        List<ProjFitnessRefreshWorkoutDetailVO> matchedRefreshWorkoutList = covertEntityList2voList(matchedWorkoutList);
        Map<Integer, ProjFitnessRefreshWorkoutDetailVO> idWorkoutGroup = matchedRefreshWorkoutList.stream()
                .collect(
                        Collectors.toMap(ProjFitnessRefreshWorkoutDetailVO::getId,
                                Function.identity(),
                                (oldValue, newValue) -> newValue )
                );
        List<ProjFitnessGenerateWorkoutDetailVO> workoutDetailList = new ArrayList<>();
        for (ProjFitnessWorkoutGenerate item : matchedWorkoutList) {
            ProjFitnessRefreshWorkoutDetailVO detailVO = idWorkoutGroup.get(item.getId());
            ProjFitnessGenerateWorkoutDetailVO generateWorkoutDetailVO = new ProjFitnessGenerateWorkoutDetailVO();
            BeanUtils.copyProperties(detailVO, generateWorkoutDetailVO);
            ProjFitnessWorkoutImagePub image = imageIdGroup.get(item.getProjFitnessWorkoutImageId());
            generateWorkoutDetailVO.setName(image.getName())
                    .setImgId(image.getId())
                    .setCoverImage(image.getCoverImage())
                    .setDetailImage(image.getDetailImage())
                    .setEventName("generate workout-" + detailVO.getId());
            workoutDetailList.add(generateWorkoutDetailVO);
        }
        return workoutDetailList;
    }

    private List<ProjFitnessWorkoutGenerate> list(FitnessWorkoutGeneratePlanReq planReq, Set<Integer> templateIdSet) {
        LambdaQueryWrapper<ProjFitnessWorkoutGenerate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessWorkoutGenerate::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessWorkoutGenerate::getProjFitnessTemplateId, templateIdSet);
        return list(wrapper);
    }

    /**
     * <p>查询自动生成的workout详情</p>
     *
     * @param idMap workoutId-imgId
     * @return java.util.List<com.laien.cmsapp.response.ProjFitnessRefreshWorkoutDetailVO>
     * <AUTHOR>
     * @date 2025/3/20 10:11
     */
    private List<ProjFitnessGenerateWorkoutDetailVO> selectDetailsByIdMap(Map<Integer, Integer> idMap) {
        // 查询workout id
        LambdaQueryWrapper<ProjFitnessWorkoutGenerate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjFitnessWorkoutGenerate::getId, idMap.keySet());
        queryWrapper.eq(ProjFitnessWorkoutGenerate::getDelFlag, GlobalConstant.NO);
        queryWrapper.eq(ProjFitnessWorkoutGenerate::getStatus, GlobalConstant.STATUS_ENABLE);

        List<ProjFitnessWorkoutGenerate> workoutList = this.list(queryWrapper);
        if (CollUtil.isEmpty(workoutList)) {
            return new ArrayList<>();
        }
        List<ProjFitnessRefreshWorkoutDetailVO> projFitnessRefreshWorkoutDetailVOS = covertEntityList2voList(workoutList);
        return this.fillImgInfo(projFitnessRefreshWorkoutDetailVOS,idMap);
    }

    /**
     * <p>查询手组的workout详情</p>
     *
     * @param idMap workoutId-imgId
     * @return java.util.List<com.laien.cmsapp.response.ProjFitnessRefreshWorkoutDetailVO>
     * <AUTHOR>
     * @date 2025/8/1 10:11
     */
    private List<ProjFitnessGenerateWorkoutDetailVO> selectManualDetailsByIdMap(Map<Integer, Integer> idMap) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();

        // 查询workout id
        LambdaQueryWrapper<ProjFitnessManualWorkoutPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjFitnessManualWorkoutPub::getId, idMap.keySet());
        queryWrapper.eq(ProjFitnessManualWorkoutPub::getDelFlag, GlobalConstant.NO);
        queryWrapper.eq(ProjFitnessManualWorkoutPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjFitnessManualWorkoutPub::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjFitnessManualWorkoutPub> workoutList = fitnessManualWorkoutPubMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(workoutList)) {
            return new ArrayList<>();
        }
        List<ProjFitnessGenerateWorkoutDetailVO> result = new ArrayList<>();
        for(ProjFitnessManualWorkoutPub pub : workoutList) {
            ProjFitnessGenerateWorkoutDetailVO generateWorkoutDetailVO = new ProjFitnessGenerateWorkoutDetailVO();
            generateManualWorkoutInfoById(versionInfoBO,generateWorkoutDetailVO,pub,idMap.get(pub.getId()));
            result.add(generateWorkoutDetailVO);
        }
        return result;
    }
    private void generateManualWorkoutInfoById(ProjPublishCurrentVersionInfoBO versionInfoBO,
                                               ProjFitnessGenerateWorkoutDetailVO replaceWork,
                                               ProjFitnessManualWorkoutPub manual,
                                               Integer imageId) {
        generateManualWorkout(versionInfoBO, replaceWork, manual);
        //通过 ID 查询 imageInfo
        LambdaQueryWrapper<ProjFitnessWorkoutImagePub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessWorkoutImagePub::getId, imageId);
        queryWrapper.eq(ProjFitnessWorkoutImagePub::getDelFlag, GlobalConstant.NO);
        queryWrapper.eq(ProjFitnessWorkoutImagePub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjFitnessWorkoutImagePub::getStatus, GlobalConstant.STATUS_ENABLE);
        ProjFitnessWorkoutImagePub imagePub = projFitnessWorkoutImagePubService.getBaseMapper().selectOne(queryWrapper);
        if(Objects.nonNull(imagePub)) {
            replaceWork.setImgId(imagePub.getId());
            replaceWork.setCoverImage(imagePub.getCoverImage());
            replaceWork.setDetailImage(imagePub.getDetailImage());
        }
    }
    private List<ProjFitnessGenerateWorkoutDetailVO> fillImgInfo(List<ProjFitnessRefreshWorkoutDetailVO> details, Map<Integer, Integer> idMap) {
        //query img info
        Map<Integer, ProjFitnessWorkoutImagePub> imgIdMap = new HashMap<>();
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        if (CollUtil.isNotEmpty(idMap)) {
            Set<Integer> imgIdSet = new HashSet<>(idMap.values());
            List<ProjFitnessWorkoutImagePub> imagePubs = projFitnessWorkoutImagePubService.list(
                    new LambdaQueryWrapper<ProjFitnessWorkoutImagePub>().in(ProjFitnessWorkoutImagePub::getId,imgIdSet)
                            .eq(ProjFitnessWorkoutImagePub::getVersion,versionInfoBO.getCurrentVersion())
            );
            projLmsI18nService.handleTextI18n(imagePubs, ProjCodeEnums.OOG104);
            imgIdMap.putAll(imagePubs.stream().collect(Collectors.toMap(ProjFitnessWorkoutImagePub::getId, Function.identity(),
                             (oldValue, newValue) -> newValue )));
        }
        List<ProjFitnessGenerateWorkoutDetailVO> workoutDetailList = new ArrayList<>();
        for (ProjFitnessRefreshWorkoutDetailVO detailVO : details) {
            ProjFitnessGenerateWorkoutDetailVO generateWorkoutDetailVO = new ProjFitnessGenerateWorkoutDetailVO();
            BeanUtils.copyProperties(detailVO, generateWorkoutDetailVO);
            if (idMap != null && idMap.containsKey(detailVO.getId()) && imgIdMap.containsKey(idMap.get(detailVO.getId()))) {
                ProjFitnessWorkoutImagePub image = imgIdMap.get(idMap.get(detailVO.getId()));
                generateWorkoutDetailVO.setName(image.getName())
                        .setImgId(image.getId())
                        .setCoverImage(image.getCoverImage())
                        .setDetailImage(image.getDetailImage())
                        .setEventName("generate workout-" + detailVO.getId());
            }
            workoutDetailList.add(generateWorkoutDetailVO);
        }
        return workoutDetailList;
    }
    private void generateManualWorkout(ProjPublishCurrentVersionInfoBO versionInfoBO, ProjFitnessGenerateWorkoutDetailVO replaceWork, ProjFitnessManualWorkoutPub manual) {
        // 查视频关联关系
        List<ProjFitnessManualWorkoutExerciseVideoPub> relationList = projFitnessManualWorkoutExerciseVideoPubMapper.selectList(
                new LambdaQueryWrapper<ProjFitnessManualWorkoutExerciseVideoPub>()
                        .eq(ProjFitnessManualWorkoutExerciseVideoPub::getProjFitnessManualWorkoutId, manual.getId())
                        .eq(ProjFitnessManualWorkoutExerciseVideoPub::getVersion, versionInfoBO.getCurrentVersion())
                        .eq(ProjFitnessManualWorkoutExerciseVideoPub::getDelFlag, GlobalConstant.NO)
                        .orderByAsc(ProjFitnessManualWorkoutExerciseVideoPub::getId)
        );

        // 按 unit 分组
        Map<ManualTypeEnums, List<ProjFitnessManualWorkoutExerciseVideoPub>> groupedRelationMap = relationList.stream()
                .collect(Collectors.groupingBy(ProjFitnessManualWorkoutExerciseVideoPub::getUnitName));

        // 查视频
        Set<Integer> videoIdSet = relationList.stream()
                .map(ProjFitnessManualWorkoutExerciseVideoPub::getProjFitnessExerciseVideoId)
                .collect(Collectors.toSet());

        List<ProjFitnessExerciseVideo> exerciseVideoList = exerciseVideoMapper.selectBatchIds(videoIdSet);
        List<ProjFitnessRefreshVideoVO> refreshVideoVOList = exerciseVideoMapStruct.toRefreshVideoVO(exerciseVideoList);
        videoService.handleVideoI18n(refreshVideoVOList);

        // 查 audio i18n
        List<ProjFitnessManualWorkoutI18nPub> audioI18nList = i18nPubService.list(new LambdaQueryWrapper<ProjFitnessManualWorkoutI18nPub>()
                .eq(ProjFitnessManualWorkoutI18nPub::getProjFitnessManualWorkoutId, manual.getId())
                .eq(ProjFitnessManualWorkoutI18nPub::getVersion, versionInfoBO.getCurrentVersion()));

        // 设置 workout 基础字段
        BeanUtil.copyProperties(manual, replaceWork);
        //更换名字
        replaceWork.setEventName("manual workout-" + replaceWork.getId());
        replaceWork.setIntensity(Collections.singletonList(manual.getIntensity()));

        // 构建 unitList
        List<ProjFitnessRefreshWorkoutDetailUnitVO> unitVOList = new ArrayList<>();
        for (Map.Entry<ManualTypeEnums, List<ProjFitnessManualWorkoutExerciseVideoPub>> entry : groupedRelationMap.entrySet()) {
            ManualTypeEnums type = entry.getKey();
            List<ProjFitnessManualWorkoutExerciseVideoPub> videoPubs = entry.getValue();

            ProjFitnessRefreshWorkoutDetailUnitVO unitVO = new ProjFitnessRefreshWorkoutDetailUnitVO();
            unitVO.setUnitName(type.getName());
            unitVO.setUnitNameType(type);
            unitVO.setCount(videoPubs.size());
            unitVO.setRounds(1);

            List<ProjFitnessRefreshVideoVO> videoList = new ArrayList<>();
            for (ProjFitnessManualWorkoutExerciseVideoPub videoPub : videoPubs) {
                for (ProjFitnessRefreshVideoVO vo : refreshVideoVOList) {
                    if (Objects.equals(vo.getId(), videoPub.getProjFitnessExerciseVideoId())) {
                        ProjFitnessRefreshVideoVO copied = new ProjFitnessRefreshVideoVO();
                        BeanUtil.copyProperties(vo, copied);
                        copied.setVideoDuration(videoPub.getVideoDuration() - videoPub.getPreviewDuration());
                        copied.setPreviewDuration(videoPub.getPreviewDuration());
                        videoList.add(copied);
                        break;
                    }
                }
            }
            unitVO.setVideoList(videoList);
            unitVOList.add(unitVO);
        }
        replaceWork.setUnitList(unitVOList);

        // 设置 audio json
        List<AudioI18nVO> audioJsonList = audioI18nList.stream().map(i18n -> {
            AudioI18nVO vo = new AudioI18nVO();
            vo.setLanguage(i18n.getLanguage());
            vo.setAudioJsonUrl(i18n.getAudioJsonUrl());
            return vo;
        }).collect(Collectors.toList());
        replaceWork.setAudioJsonList(audioJsonList);
    }

    private List<ProjFitnessRefreshWorkoutDetailVO> covertEntityList2voList(List<ProjFitnessWorkoutGenerate> workoutList) {
        // 查询workout 和 video 关联关系
        List<Integer> workoutIdList = workoutList.stream().map(BaseModel::getId).collect(Collectors.toList());
        LambdaQueryWrapper<ProjFitnessWorkoutGenerateExerciseVideo> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper.in(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessWorkoutGenerateId, workoutIdList);
        relationQueryWrapper.eq(ProjFitnessWorkoutGenerateExerciseVideo::getDelFlag, GlobalConstant.NO);
        relationQueryWrapper.orderByAsc(ProjFitnessWorkoutGenerateExerciseVideo::getId);

        List<ProjFitnessWorkoutGenerateExerciseVideo> relationList = projFitnessWorkoutGenerateExerciseVideoMapper.selectList(relationQueryWrapper);
        Set<Integer> videoIdList = relationList.stream().map(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessExerciseVideoId).collect(Collectors.toSet());
        // 关联表对应的group，以便于填充group相关数据
        Set<Integer> groupIdSet = relationList.stream().map(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessTemplateExerciseGroupId).collect(Collectors.toSet());
        List<ProjFitnessTemplateExerciseGroup> exerciseGroupList = projFitnessTemplateExerciseGroupMapper.selectBatchIds(groupIdSet);
        projLmsI18nService.handleTextI18n(exerciseGroupList, ProjCodeEnums.OOG104);
        // 查询video
        List<ProjFitnessExerciseVideo> exerciseVideoList = exerciseVideoMapper.selectBatchIds(videoIdList);
        List<ProjFitnessRefreshVideoVO> refreshVideoVOList = exerciseVideoMapStruct.toRefreshVideoVO(exerciseVideoList);
        videoService.handleVideoI18n(refreshVideoVOList);
        //query i18n audio Info
        Map<Integer, List<ProjFitnessWorkoutGenerateI18n>> audioI18nMap = i18nService.list(new LambdaQueryWrapper<ProjFitnessWorkoutGenerateI18n>()
                .in(ProjFitnessWorkoutGenerateI18n::getProjFitnessWorkoutGenerateId, workoutIdList)
        ).stream().collect(Collectors.groupingBy(ProjFitnessWorkoutGenerateI18n::getProjFitnessWorkoutGenerateId));
        // 组装数据
        return workoutList.stream().map(entity -> {
            ProjFitnessRefreshWorkoutDetailVO refreshWorkoutDetailVO = projFitnessWorkoutGenerateMapStruct.toRefreshWorkoutDetailVO(entity);
            // 过滤出该workout对应模板下的所有group
            List<ProjFitnessRefreshWorkoutDetailUnitVO> unitVOList = exerciseGroupList.stream()
                    .filter(group -> Objects.equals(group.getProjFitnessTemplateId(), entity.getProjFitnessTemplateId()))
                    .map(group -> {
                        // 填充unit
                        ProjFitnessRefreshWorkoutDetailUnitVO unitVO = new ProjFitnessRefreshWorkoutDetailUnitVO();
                        unitVO.setUnitName(group.getGroupName());
                        unitVO.setUnitNameType(group.getGroupType());
                        unitVO.setCount(group.getCount());
                        unitVO.setRounds(group.getRounds());
                        // 填充视频
                        List<ProjFitnessRefreshVideoVO> refreshVideoList = relationList.stream()
                                // workout下该分组的视频ID
                                .filter(relation -> Objects.equals(relation.getProjFitnessWorkoutGenerateId(), entity.getId()) && Objects.equals(relation.getProjFitnessTemplateExerciseGroupId(), group.getId()))
                                // 该ID对应的视频
                                .map(relation ->
                                        refreshVideoVOList.stream().filter(videoVO ->
                                                Objects.equals(relation.getProjFitnessExerciseVideoId(), videoVO.getId())).findAny().map(video -> {
                                                    //返回的video duration只算正式锻炼时间（排除preview duration时长）
                                                    video.setVideoDuration(relation.getVideoDuration() - relation.getPreviewDuration());
                                                    video.setPreviewDuration(relation.getPreviewDuration());
                                                    return video;
                                        }))
                                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
                        unitVO.setVideoList(refreshVideoList);
                        return unitVO;
                    }).collect(Collectors.toList());
            refreshWorkoutDetailVO.setUnitList(unitVOList);
            //other language
            List<ProjFitnessWorkoutGenerateI18n> i18nAudios = audioI18nMap.getOrDefault(entity.getId(), Collections.emptyList());
            List<AudioI18nVO> audioJsonList = new ArrayList();
            i18nAudios.forEach(i18n -> {
                AudioI18nVO audioI18nVO = new AudioI18nVO();
                audioI18nVO.setLanguage(i18n.getLanguage()).setAudioJsonUrl(i18n.getAudioJsonUrl());
                audioJsonList.add(audioI18nVO);
            });
            refreshWorkoutDetailVO.setAudioJsonList(audioJsonList);
            return refreshWorkoutDetailVO;
        }).collect(Collectors.toList());
    }
}
