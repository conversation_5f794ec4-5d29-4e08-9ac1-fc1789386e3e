package com.laien.cmsapp.oog104.controller;

import cn.hutool.core.collection.CollUtil;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.request.DailyHabitReq;
import com.laien.cmsapp.oog104.request.FitnessWorkoutGeneratePlanReq;
import com.laien.cmsapp.oog104.request.PlanWorkoutRefreshWrapperReq;
import com.laien.cmsapp.oog104.response.ProjFitnessDailyHabitWorkoutDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessGenerateWorkoutDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessGenerateWorkoutPlanVO;
import com.laien.cmsapp.oog104.service.IProjFitnessWorkoutGenerateService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.FitnessUserTypeEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

import static com.laien.cmsapp.oog104.service.impl.ProjFitnessWorkoutImagePubServiceImpl.PLAN_DAY;
import static com.laien.common.oog104.enums.template.ExclusiveTypeEnums.*;

/**
 * <AUTHOR>
 * @since 2025/3/17
 */
@Slf4j
@Api(tags = "app端：fitnessWorkoutGenerate")
@RestController
@RequestMapping("/oog104/fitnessWorkoutGenerate")
public class ProjFitnessWorkoutGenerateController extends ResponseController {

    @Resource
    private IProjFitnessWorkoutGenerateService projFitnessWorkoutGenerateService;

    @ApiOperation(value = "fitnessGenerateWorkout plan v1")
    @GetMapping("/v1/plan")
    public ResponseResult<ProjFitnessGenerateWorkoutPlanVO> plan(FitnessWorkoutGeneratePlanReq planReq) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        if(Objects.isNull(planReq.getUserType()))
           planReq.setUserType(FitnessUserTypeEnums.OLD_USER);

        //移除 special limit 中的 None
        if(CollUtil.isNotEmpty(planReq.getSpecialLimitSet())) {
            planReq.getSpecialLimitSet().remove(ExerciseVideoSpecialLimitEnums.NONE);
        }
        //添加逻辑,当 template type 为 106 fitness,设置特殊为 normal
        if(Objects.equals(planReq.getTemplateType(), TemplateTypeEnums.FITNESS_106) ) {
            planReq.setExclusiveType(NORMAL);
            planReq.setSpecialLimitSet(Collections.emptySet());
        }
        ProjFitnessGenerateWorkoutPlanVO plan = projFitnessWorkoutGenerateService.plan(planReq, versionInfoBO);
        List<ProjFitnessGenerateWorkoutDetailVO> workoutDetailList = plan.getWorkoutDetailList();
        if(null == workoutDetailList){
            workoutDetailList = new ArrayList<>();
        }
        if(CollUtil.isEmpty(workoutDetailList) || workoutDetailList.size() < PLAN_DAY) {
            log.error("fitness generate workout plan v1 not match enough workout detail,workout count:{},planReq:{}", workoutDetailList.size(), planReq);
        }
        return succ(plan);
    }

    @ApiOperation(value = "fitnessGenerateWorkout Daily Habit v1")
    @GetMapping("/v1/dailyHabit")
    public ResponseResult<List<ProjFitnessDailyHabitWorkoutDetailVO>> dailyHabit(DailyHabitReq req) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjFitnessDailyHabitWorkoutDetailVO> dailyHabitList = new ArrayList<>(200);
        List<ExclusiveTypeEnums> exclusiveTypeList = Arrays.asList(DAILY_STRETCH, DAILY_HIIT, DAILY_ABS, DAILY_BOOTY, DAILY_FAT_LOSS);
        for (ExclusiveTypeEnums exclusiveType : exclusiveTypeList) {
            List<ProjFitnessDailyHabitWorkoutDetailVO> workoutList = projFitnessWorkoutGenerateService.dailyHabitList(req, versionInfoBO, exclusiveType);
            if (CollUtil.isEmpty(workoutList) || workoutList.size() < 16) {
                log.error("daily habit workout not match enough workout detail,workout count:{},req:{},exclusive type:{}", dailyHabitList.size(), req, exclusiveType);
            }
            if (CollUtil.isNotEmpty(workoutList)) {
                dailyHabitList.addAll(workoutList);
            }
        }

        return succ(dailyHabitList);
    }


    @ApiOperation(value = "fitnessGenerateWorkout detail v1")
    @GetMapping("/v1/detailList")
    public ResponseResult<List<ProjFitnessGenerateWorkoutDetailVO>> detailList(PlanWorkoutRefreshWrapperReq req) {

        return succ(projFitnessWorkoutGenerateService.detailList(req.getReq()));
    }
}
