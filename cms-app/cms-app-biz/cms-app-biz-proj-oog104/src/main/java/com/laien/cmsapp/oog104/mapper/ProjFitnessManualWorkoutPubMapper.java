package com.laien.cmsapp.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog104.entity.ProjFitnessManualWorkoutPub;
import com.laien.cmsapp.oog104.request.FitnessWorkoutGeneratePlanReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * proj_fitness_manual_workout_pub Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/18
 */
@Mapper
public interface ProjFitnessManualWorkoutPubMapper extends BaseMapper<ProjFitnessManualWorkoutPub> {
   List<ProjFitnessManualWorkoutPub> getManualWorkoutByType(@Param("req") FitnessWorkoutGeneratePlanReq planReq,
   @Param("min") int min,@Param("max") int max,@Param("versionId") Integer versionId,@Param("ids")List<Integer> ids);
}
