package com.laien.cmsapp.oog104.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateIntensityEnums;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * proj_fitness_workout_image
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "proj_fitness_workout_image_pub", autoResultMap = true)
@Data
public class ProjFitnessWorkoutImagePub extends BaseModel  implements AppTextCoreI18nModel {

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_WORKOUT_IMAGE;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty("图片名称")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty("针对人群类型")
    private ExclusiveTypeEnums exclusiveType;

    //v8.3.0新增templateType字段
    @ApiModelProperty("模版类型字段")
    private TemplateTypeEnums templateType;

    @ApiModelProperty("年龄段")
    @TableField(typeHandler = ManualAgeGroupEnums.TypeHandler.class)
    private List<ManualAgeGroupEnums> ageGroup;

    @ApiModelProperty("锻炼部位")
    private ManualTargetEnums target;

    @ApiModelProperty("难度")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty("排序")
    private TemplateIntensityEnums intensity;

    @ApiModelProperty("特殊限制")
    @TableField(typeHandler = ExerciseVideoSpecialLimitEnums.TypeHandler.class)
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty("封面图片地址")
    private String coverImage;

    @ApiModelProperty("详情图片地址")
    private String detailImage;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("项目id")
    private Integer projId;

    @ApiModelProperty("排序字段")
    private Integer sortNo;

    @ApiModelProperty("workout id 数组")
    private String workoutIds;

}
