<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.laien.cmsapp.oog104.mapper.ProjFitnessManualWorkoutPubMapper">
    <!-- 定义resultMap处理TypeHandler字段 -->
    <resultMap id="ProjFitnessManualWorkoutPubMap" type="com.laien.cmsapp.oog104.entity.ProjFitnessManualWorkoutPub">
        <result column="ageGroup" property="ageGroup" typeHandler="com.laien.common.oog104.enums.manual.ManualAgeGroupEnums$TypeHandler"/>
        <result column="target" property="target" typeHandler="com.laien.common.oog104.enums.manual.ManualTargetEnums$TypeHandler"/>
        <result column="equipment" property="equipment" typeHandler="com.laien.common.oog104.enums.manual.ManualEquipmentEnums$TypeHandler"/>
        <result column="special_Limit" property="specialLimit" typeHandler="com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums$TypeHandler"/>
        <result column="category" property="category" typeHandler="com.laien.common.oog104.enums.manual.ManualCategoryEnums$TypeHandler"/>

    </resultMap>

    <select id="getManualWorkoutByType" resultMap="ProjFitnessManualWorkoutPubMap">
      SELECT
            *
      FROM
            proj_fitness_manual_workout_pub fmwp
      WHERE
            fmwp.duration &gt; #{min}
        AND fmwp.duration &lt; #{max}
        AND fmwp.difficulty = #{req.difficulty}
        AND FIND_IN_SET(#{req.ageGroup},fmwp.age_group) >0
        AND fmwp.STATUS = 1
        AND fmwp.del_flag = 0
        AND FIND_IN_SET( 3, fmwp.category ) >0
        AND version = #{versionId}
        <if test="ids != null and ids.size() > 0">
        AND fmwp.id not IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        </if>
</select>
</mapper>
