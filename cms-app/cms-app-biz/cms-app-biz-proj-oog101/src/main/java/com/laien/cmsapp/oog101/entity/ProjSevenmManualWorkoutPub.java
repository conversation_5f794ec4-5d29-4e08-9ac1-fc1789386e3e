package com.laien.cmsapp.oog101.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog101.enums.*;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 7M Manual Workout
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjSevenmManualWorkout对象", description = "proj_sevenm_manual_workout")
@TableName(autoResultMap = true)
public class ProjSevenmManualWorkoutPub extends BaseModel  implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "Exercise Name")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty("表标识")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_SEVENM_MANUAL_WORKOUT;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "Event Name, auto-generated")
    private String eventName;

    @ApiModelProperty(value = "Cover Image, supports png/webp formats")
    private String coverImage;

    @ApiModelProperty(value = "Detail Image, supports png/webp formats")
    private String detailImage;

    @ApiModelProperty(value = "supplementImage, supports png/webp formats")
    private String supplementImage;

    @ApiModelProperty(value = "sketchImage, supports png/webp formats")
    private String sketchImage;

    @ApiModelProperty(value = "Workout Type")
    private SevenmTemplateTypeEnums workoutType;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "Difficulty")
    private SevenmDifficultyEnums difficulty;

    @ApiModelProperty(value = "Target Areas")
    @TableField(typeHandler = SevenmTargetEnums.TypeHandler.class)
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "Categories")
    @TableField(typeHandler = SevenmWorkoutCategoryEnums.TypeHandler.class)
    private List<SevenmWorkoutCategoryEnums> category;

    @ApiModelProperty(value = "Description, maximum 1000 characters")
    @AppTextTranslateField
    private String description;

    @ApiModelProperty(value = "Duration in milliseconds")
    private Integer duration;

    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period Start")
    private LocalDateTime newTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period End")
    private LocalDateTime newTimeEnd;

    @ApiModelProperty(value = "Subscription 0-No, 1-Yes")
    private Integer subscription;

    @ApiModelProperty(value = "Status")
    private Integer status;

    @ApiModelProperty(value = "File Status: 0-Running, 1-Success, 2-Failed")
    private Integer fileStatus;

    @ApiModelProperty(value = "Failure Message")
    private String failMessage;

    @ApiModelProperty(value = "Audio Languages, comma separated")
    private String audioLanguages;

    @ApiModelProperty(value = "Video URL")
    private String videoUrl;
}
