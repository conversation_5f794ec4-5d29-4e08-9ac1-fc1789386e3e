package com.laien.cmsapp.oog101.request;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.laien.cmsapp.oog101.entity.ProjSevenmManualWorkoutPub;
import com.laien.cmsapp.requst.LangReq;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmWorkoutCategoryEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *  ProjSevenmManualWorkoutListReq
 *
 * <AUTHOR>
 * @since 2025/05/20
 */
@Data
@ApiModel(value = "manual workout list 请求", description = "manual workout list 请求")
public class ProjSevenmManualWorkoutListReq extends LangReq {

    @ApiModelProperty(value = "workout category")
    private SevenmWorkoutCategoryEnums category;

    @ApiModelProperty(value = "gender")
    private SevenmGenderEnums gender;
    /**
     * 用于内部排序的字段，不暴露给Swagger
     */
    @ApiModelProperty(hidden = true)
    private SFunction<ProjSevenmManualWorkoutPub, ?> orderByDescColumn;

}
