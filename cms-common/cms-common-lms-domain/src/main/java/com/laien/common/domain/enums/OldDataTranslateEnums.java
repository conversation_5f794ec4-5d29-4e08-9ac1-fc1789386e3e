package com.laien.common.domain.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/5
 */
@Getter
public enum OldDataTranslateEnums {

    /*101*/
    PROJ_SEVENM_EXERCISE_VIDEO("projSevenmExerciseVideoServiceImpl",ProjCodeEnums.OOG101),
    PROJ_SEVENM_FASTING_ARTICLE("projSevenmFastingArticleServiceImpl",ProjCodeEnums.OOG101),
    PROJ_SEVENM_MANUAL_WORKOUT("projSevenmManualWorkoutServiceImpl",ProjCodeEnums.OOG101),
    PROJ_SEVENM_PLAYLIST("projSevenmPlaylistServiceImpl",ProjCodeEnums.OOG101),
    PROJ_SEVENM_SOUND("projSevenmSoundServiceImpl",ProjCodeEnums.OOG101),
    PROJ_CATEGORY116("projCategory116ServiceImpl", ProjCodeEnums.OOG116),
    PROJ_COACH116("projCoach116ServiceImpl", ProjCodeEnums.OOG116),
    PROJ_PROGRAM116("projProgram116ServiceImpl", ProjCodeEnums.OOG116),
    PROJ_SOUND116("projSound116ServiceImpl", ProjCodeEnums.OOG116),
    PROJ_TEMPLATE116_RULE("projTemplate116RuleServiceImpl", ProjCodeEnums.OOG116),
    PROJ_WORKOUT116("projWorkout116ServiceImpl", ProjCodeEnums.OOG116),
    PROJ_WORKOUT116_RES_VIDEO116("projWorkout116ResVideo116ServiceImpl", ProjCodeEnums.OOG116),
    RES_VIDEO116("resVideo116ServiceImpl", ProjCodeEnums.OOG116),
//    RES_IMAGE("resImageServiceImpl", ProjCodeEnums.OOG116),

    // 200 video
    PROJ_YOGA_VIDEO("resYogaVideoServiceImpl",ProjCodeEnums.OOG200),
//    PROJ_YOGA_TRANSITION("resTransitionServiceImpl",ProjCodeEnums.OOG200),
    PROJ_CHAIR_YOGA_VIDEO("projChairYogaVideoServiceImpl",ProjCodeEnums.OOG200),
    PROJ_WALL_PILATES_VIDEO("projWallPilatesVideoServiceImpl",ProjCodeEnums.OOG200),
    PROJ_YOGA_POSE_VIDEO("projYogaPoseVideoServiceImpl",ProjCodeEnums.OOG200),

    // 200 video class
    PROJ_VIDEO_CLASS("resVideoClassServiceImpl",ProjCodeEnums.OOG200),
    PROJ_COLLECTION_CLASS("projCollectionClassServiceImpl",ProjCodeEnums.OOG200),
    PROJ_COLLECTION_TEACHER("projCollectionTeacherServiceImpl",ProjCodeEnums.OOG200),
    PROJ_POSE_LIBRARY("resPoseLibraryServiceImpl",ProjCodeEnums.OOG200),

    // 200 meal and fasting
    PROJ_DISH("projDishServiceImpl",ProjCodeEnums.OOG200),
    PROJ_DISH_COLLECTION("projDishCollectionServiceImpl",ProjCodeEnums.OOG200),
    PROJ_DISH_STEP("projDishStepServiceImpl",ProjCodeEnums.OOG200),
    PROJ_DISH_STEP_TIP("projDishStepTipServiceImpl",ProjCodeEnums.OOG200),
    PROJ_MEAL_PLAN("projMealPlanServiceImpl",ProjCodeEnums.OOG200),
    PROJ_ALLERGEN("projAllergenServiceImpl",ProjCodeEnums.OOG200),
    PROJ_INGREDIENT("projIngredientServiceImpl",ProjCodeEnums.OOG200),
    PROJ_FASTING_ARTICLE("projFastingArticleServiceImpl",ProjCodeEnums.OOG200),

    // 200 playlist
    PROJ_YOGA_MUSIC("projYogaMusicServiceImpl",ProjCodeEnums.OOG200),
    PROJ_YOGA_PLAYLIST("projYogaPlaylistServiceImpl",ProjCodeEnums.OOG200),
    PROJ_YOGA_QUOTE("projYogaQuoteServiceImpl",ProjCodeEnums.OOG200),

    // 200 workout
//    PROJ_YOGA_AUTO_WORKOUT("projYogaAutoWorkoutServiceImpl",ProjCodeEnums.OOG200),
    PROJ_YOGA_REGULAR_WORKOUT("projYogaRegularWorkoutServiceImpl", ProjCodeEnums.OOG200),
    PROJ_AUTO_WORKOUT_BASIC_INFO("projAutoWorkoutBasicInfoServiceImpl", ProjCodeEnums.OOG200),
    PROJ_CHAIR_YOGA_REGULAR_WORKOUT("projChairYogaRegularWorkoutServiceImpl", ProjCodeEnums.OOG200),
    PROJ_WALL_PILATES_REGULAR_WORKOUT("projWallPilatesRegularWorkoutServiceImpl", ProjCodeEnums.OOG200),
    PROJ_YOGA_PROGRAM("projYogaProgramServiceImpl", ProjCodeEnums.OOG200),
    PROJ_YOGA_POSE_WORKOUT("projYogaPoseWorkoutServiceImpl", ProjCodeEnums.OOG200),
    PROJ_YOGA_POSE_GROUP("projYogaPoseGroupServiceImpl", ProjCodeEnums.OOG200),

    // 200 new
    PROJ_SOUND("projSoundServiceImpl",ProjCodeEnums.OOG200),

    /*104*/
    PROJ_FITNESS_EXERCISE_VIDEO("projFitnessExerciseVideoServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_VIDEO("projFitnessVideoServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_WORKOUT_PROJ_FITNESS_VIDEO("projFitnessWorkoutProjFitnessVideoServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_SOUND("projFitnessSoundServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_WORKOUT("projFitnessWorkoutServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_PLAN("projFitnessPlanServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_COLLECTION("projFitnessCollectionServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_MANUAL_WORKOUT("projFitnessManualWorkoutServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_WORKOUT_IMAGE("projFitnessWorkoutImageServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_TEMPLATE_EXERCISE_GROUP("projFitnessTemplateExerciseGroupServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_MEAL_PLAN("projFitnessMealPlanServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_DISH_COLLECTION("projFitnessDishCollectionServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_DISH("projFitnessDishServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_DISH_STEP("projFitnessDishStepServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_DISH_STEP_TIP("projFitnessDishStepTipServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_ALLERGEN("projFitnessAllergenServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_INGREDIENT("projFitnessIngredientServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_FASTING_ARTICLE("projFitnessFastingArticleServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_VIDEO_COURSE("projFitnessVideoCourseServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_COACHING_COURSES("projFitnessCoachingCoursesServiceImpl",ProjCodeEnums.OOG104),
    PROJ_FITNESS_COACH("projFitnessCoachServiceImpl",ProjCodeEnums.OOG104),

    // common, 但使用语言最多的104
    RES_PLAYLIST("projPlaylistServiceImpl",ProjCodeEnums.OOG104),
    RES_PLAYLIST_MUSIC("projPlaylistMusicServiceImpl",ProjCodeEnums.OOG104),
    RES_IMAGE("resImageServiceImpl", ProjCodeEnums.OOG104),
    ;

    private final String serviceName;
    private final List<ProjCodeEnums> projCodeEnums;

    OldDataTranslateEnums(String serviceName, ProjCodeEnums... projCodeEnums) {
        this.serviceName = serviceName;
        this.projCodeEnums = Arrays.asList(projCodeEnums);
    }

    public static List<OldDataTranslateEnums> getEnumsListByProjCode(ProjCodeEnums projCodeEnums) {
        return Arrays.stream(OldDataTranslateEnums.values()).filter(
                oldDataTranslateEnums -> oldDataTranslateEnums.getProjCodeEnums().contains(projCodeEnums))
                .collect(Collectors.toList());
    }
}
