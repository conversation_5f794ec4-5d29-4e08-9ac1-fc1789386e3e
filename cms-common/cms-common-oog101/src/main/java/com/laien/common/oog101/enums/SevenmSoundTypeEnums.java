package com.laien.common.oog101.enums;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.List;
@Getter
@AllArgsConstructor
public enum SevenmSoundTypeEnums implements IEnumBase {
    REGULAR_FITNESS(1, "Regular Fitness",
            ListUtil.of(SevenmSoundSubTypeEnums.PROMPT,SevenmSoundSubTypeEnums.BASIC,SevenmSoundSubTypeEnums.COMPLETE,SevenmSoundSubTypeEnums.WELCOME)),
    DAILY_STEP(2, "Daily Step",
            ListUtil.of(
                    SevenmSoundSubTypeEnums.FREE_WALK_BEGIN,
                    SevenmSoundSubTypeEnums.FREE_WALK_PROGRESS_5MIN,
                    SevenmSoundSubTypeEnums.FREE_WALK_PROGRESS_15MIN,
                    SevenmSoundSubTypeEnums.FREE_WALK_PROGRESS_30MIN,
                    SevenmSoundSubTypeEnums.FREE_WALK_REACH_GOAL,
                    SevenmSoundSubTypeEnums.FREE_WALK_COMPLETED,
                    SevenmSoundSubTypeEnums.TARGET_WALK_BEGIN,
                    SevenmSoundSubTypeEnums.TARGET_WALK_PROGRESS_25,
                    SevenmSoundSubTypeEnums.TARGET_WALK_PROGRESS_50,
                    SevenmSoundSubTypeEnums.TARGET_WALK_PROGRESS_75,
                    SevenmSoundSubTypeEnums.TARGET_WALK_REACH_GOAL,
                    SevenmSoundSubTypeEnums.BURNING_WALK_BEGIN,
                    SevenmSoundSubTypeEnums.BURNING_WALK_PROGRESS_SLOW,
                    SevenmSoundSubTypeEnums.BURNING_WALK_PROGRESS_FASTER,
                    SevenmSoundSubTypeEnums.BURNING_WALK_COMPLETED,
                    SevenmSoundSubTypeEnums.MINDFUL_WALK_BEGIN_BEACH,
                    SevenmSoundSubTypeEnums.MINDFUL_WALK_BEGIN_FOREST,
                    SevenmSoundSubTypeEnums.MINDFUL_WALK_BEGIN_CREEK,
                    SevenmSoundSubTypeEnums.MINDFUL_WALK_BEGIN_VALLEY,
                    SevenmSoundSubTypeEnums.MINDFUL_WALK_PROGRESS,
                    SevenmSoundSubTypeEnums.MINDFUL_WALK_COMPLETED_BEACH,
                    SevenmSoundSubTypeEnums.MINDFUL_WALK_COMPLETED_FOREST,
                    SevenmSoundSubTypeEnums.MINDFUL_WALK_COMPLETED_CREEK,
                    SevenmSoundSubTypeEnums.MINDFUL_WALK_COMPLETED_VALLEY
            )),
    ;


    @EnumValue
    private final Integer code;
    private final String name;
    private final List<SevenmSoundSubTypeEnums> soundSubTypes;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmSoundTypeEnums> {
    }
}
