package com.laien.common.oog101.enums;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Getter
@AllArgsConstructor
public enum SevenmTemplateTypeEnums implements IEnumBase {
    SEVENM_NORMAL_WORKOUT(1, "7M Normal Workout",3,
            ListUtil.of(new TemplateGroupConfig(SevenmTypeEnums.WARM_UP,1,1),new TemplateGroupConfig(SevenmTypeEnums.MAIN,10,1))),
    SEVENM_STRETCH_WORKOUT(2, "7M Stretch Workout",1,
            ListUtil.of(new TemplateGroupConfig(SevenmTypeEnums.COOL_DOWN,3,1))),
    REGULAR_WORKOUT(3, "Regular Workout",1,
            ListUtil.of(new TemplateGroupConfig(SevenmTypeEnums.WARM_UP,0,1),
                    new TemplateGroupConfig(SevenmTypeEnums.MAIN,0,1),
                    new TemplateGroupConfig(SevenmTypeEnums.COOL_DOWN,0,1))),

    ;
    @EnumValue
    private final Integer code;
    private final String name;
    private final Integer workoutCountPerday;
    private final List<TemplateGroupConfig> groupConfigs;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmTemplateTypeEnums> {
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<SevenmTemplateTypeEnums> {
    }

    @Data
    @AllArgsConstructor
    public static class TemplateGroupConfig{
        public SevenmTypeEnums type;
        public Integer count;
        public Integer round;
    }
}
