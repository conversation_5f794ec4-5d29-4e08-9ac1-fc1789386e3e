package com.laien.common.oog101.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * Proj7MGenderEnums
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
public enum SevenmGenderEnums implements IEnumBase,SevenmEnumConvertible {
    FEMALE(1,"Female","Female"),
    MALE(2,"Male","Male"),
    BOTH(3,"Both","Both");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    SevenmGenderEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmGenderEnums> {
    }

    @Override
    public Enum<?> getDefaultValue() {
        return FEMALE;
    }

    public static SevenmGenderEnums safeValueOf(String name) {
        return SevenmEnumConvertible.safeValueOf(SevenmGenderEnums.class, name);
    }
}
