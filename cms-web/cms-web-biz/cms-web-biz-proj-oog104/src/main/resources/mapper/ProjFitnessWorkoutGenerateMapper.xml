<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog104.mapper.ProjFitnessWorkoutGenerateMapper">

    <sql id="queryTemplate">
        FROM proj_fitness_workout_generate wg
        <if test="pageReq.videoIdList != null and pageReq.videoIdList.size() > 0">
            JOIN proj_fitness_workout_generate_exercise_video v ON v.proj_fitness_workout_generate_id = wg.id
        </if>
        WHERE
        wg.del_flag = 0
        <if test="pageReq.videoIdList != null and pageReq.videoIdList.size() > 0">
            AND v.del_flag = 0
            AND v.proj_fitness_exercise_video_id IN
            <foreach item="item" collection="pageReq.videoIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pageReq.projFitnessTemplateId != null">
            AND wg.proj_fitness_template_id = #{pageReq.projFitnessTemplateId}
        </if>
        <if test="pageReq.id != null">
            AND wg.id = #{pageReq.id}
        </if>
        <if test="pageReq.status != null">
            AND wg.status = #{pageReq.status}
        </if>
        <if test="pageReq.fileStatus != null">
            AND wg.file_status = #{pageReq.fileStatus}
        </if>
        <if test="templateIdSet != null and templateIdSet.size() > 0">
            AND wg.proj_fitness_template_id IN
            <foreach item="item" collection="templateIdSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pageReq.target != null">
            AND wg.target = #{pageReq.target}
        </if>
        <if test="pageReq.difficulty != null">
            AND wg.difficulty = #{pageReq.difficulty}
        </if>
        <if test="pageReq.equipment != null">
            AND wg.equipment = #{pageReq.equipment}
        </if>
        <if test="pageReq.specialLimit != null and pageReq.specialLimit.size()>0">
            <foreach collection="pageReq.specialLimit" item="item">
                AND FIND_IN_SET(#{item}, wg.special_limit)
            </foreach>
        </if>
        <if test="pageReq.intensity != null and pageReq.intensity.size()>0">
            <foreach collection="pageReq.intensity" item="item">
                AND FIND_IN_SET(#{item}, wg.intensity)
            </foreach>
        </if>
        <if test="pageReq.projId!= null">
            AND wg.proj_id = #{pageReq.projId}
        </if>
        <if test="minDuration!= null">
            AND wg.duration >= #{minDuration}
        </if>
        <if test="maxDuration!= null">
            AND wg.duration <![CDATA[ <= ]]> #{maxDuration}
        </if>
        <if test="pageReq.absRateMin != null">
            AND wg.abs_rate >= #{pageReq.absRateMin}
        </if>
        <if test="pageReq.absRateMax != null">
            AND wg.abs_rate <![CDATA[ <= ]]> #{pageReq.absRateMax}
        </if>
        <if test="pageReq.standingRateMin != null">
            AND wg.standing_rate >= #{pageReq.standingRateMin}
        </if>
        <if test="pageReq.standingRateMax != null">
            AND wg.standing_rate <![CDATA[ <= ]]> #{pageReq.standingRateMax}
        </if>
        GROUP BY wg.id
        ORDER BY wg.id DESC
    </sql>

    <select id="page" resultType="java.lang.Integer">
        SELECT
            wg.id
        <include refid="queryTemplate" />

    </select>
    <select id="list" resultType="java.lang.Integer">
        SELECT
            wg.id
        <include refid="queryTemplate" />
    </select>

</mapper>
