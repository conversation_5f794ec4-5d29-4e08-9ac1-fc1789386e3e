package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.common.oog104.enums.manual.*;
import com.laien.common.oog104.enums.template.TemplateTaskStatusEnum;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.oog104.bo.*;
import com.laien.web.biz.proj.oog104.config.FitnessSoundConfig;
import com.laien.web.biz.proj.oog104.config.Oog104BizConfig;
import com.laien.web.biz.proj.oog104.entity.*;
import com.laien.web.biz.proj.oog104.entity.i18n.ProjFitnessExerciseVideoI18n;
import com.laien.web.biz.proj.oog104.entity.i18n.ProjFitnessSoundI18n;
import com.laien.web.biz.proj.oog104.enums.AudioCategoryEnums;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessTemplateExerciseGroupMapper;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessWorkoutGenerateMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessExerciseVideoMapStruct;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessWorkoutGenerateMapStruct;
import com.laien.web.biz.proj.oog104.request.ProjFitnessManualWorkoutGenerateM3u8Req;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutGeneratePageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutGenerateUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoPageVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutGenerateDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutGeneratePageVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutGenerateVideoVO;
import com.laien.web.biz.proj.oog104.service.*;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.common.user.model.LoginUserInfo;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.async.service.IAsyncService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.web.frame.constant.GlobalConstant.SECOND_MILL;

/**
 * <p>
 * proj_fitness_workout_generate 服务实现类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjFitnessWorkoutGenerateServiceImpl extends ServiceImpl<ProjFitnessWorkoutGenerateMapper, ProjFitnessWorkoutGenerate>
        implements IProjFitnessWorkoutGenerateService {

    private final ProjFitnessWorkoutGenerateMapStruct mapStruct;
    private final ProjFitnessExerciseVideoMapStruct videoMapStruct;
    private final IProjFitnessWorkoutGenerateExerciseVideoService generateVideoService;
    private final ProjFitnessTemplateService templateService;
    private final ProjFitnessTemplateTaskService taskService;
    private final IProjFitnessExerciseVideoService videoService;
    private final ProjFitnessTemplateExerciseGroupMapper groupMapper;
    private final ProjFitnessTemplateTaskService templateTaskService;
    private final ProjFitnessTemplateExerciseGroupService exerciseGroupService;
    private final FileService fileService;
    private final Oog104BizConfig oog104BizConfig;
    private final IAsyncService asyncService;
    private final TransactionTemplate transactionTemplate;
    private final IProjFitnessWorkoutGenerateI18nService workoutGenerateI18nService;
    private final IProjInfoService projInfoService;

    private static final String AUDIO_DIR_KEY = "project-fitness-workout-generate-json";
    private static final String M3U8_DIR_KEY = "project-fitness-workout-generate-m3u8";
    private static final int VIDEO_DURATION = 10000;
    private static final int PREVIEW_ROUND = 1;

    private static final ThreadPoolTaskExecutor poolTaskExecutor = createTaskExecutor4Generate();
    private static final ThreadPoolTaskExecutor generateM3u8Executor = createGenerateM3u8Executor();
    private final AtomicBoolean generateM3u8ReqIsRunning = new AtomicBoolean(false);
    private final LinkedBlockingQueue<ProjFitnessManualWorkoutGenerateM3u8Req> generateM3u8Queue = new LinkedBlockingQueue<>(GlobalConstant.TWO_THOUSAND);
    private final ICoreSpeechTaskI18nPubService speechI18nPubService;


    private static ThreadPoolTaskExecutor createTaskExecutor4Generate() {

        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(24);
        threadPoolTaskExecutor.setMaxPoolSize(24);
        threadPoolTaskExecutor.setQueueCapacity(2000);
        threadPoolTaskExecutor.setKeepAliveSeconds(60);
        // 允许回收核心线程
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        threadPoolTaskExecutor.setThreadNamePrefix("fitness-workout-generate-thread-");
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }


    private static ThreadPoolTaskExecutor createGenerateM3u8Executor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(24);
        threadPoolTaskExecutor.setMaxPoolSize(24);
        threadPoolTaskExecutor.setQueueCapacity(2000);
        threadPoolTaskExecutor.setKeepAliveSeconds(60);
        // 允许回收核心线程
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        threadPoolTaskExecutor.setThreadNamePrefix("fitness-workout-generateM3u8-thread-");
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Override
    public void generateWorkoutByTask(List<ProjFitnessTemplateTask> templateTasks, ProjInfo projInfo) {

        try {
            // 1. check task and template
            List<String> languageList = getLanguageList(projInfo);
            List<FitnessTemplateTaskBO> templateTaskBOList = checkTemplateTask4Generate(templateTasks, languageList);

            // 2. check video and create BO
            FitnessWorkoutGenerateVideoBO generateVideoBO = createWorkoutGenerateVideoBO(languageList);

            // 3. check audio and create BO
            Set<ManualExerciseTypeEnums> exerciseTypeList = templateTaskBOList.stream().map(taskBO -> taskBO.getTemplate().getTemplateType().getExerciseType()).collect(Collectors.toSet());
            Map<ManualExerciseTypeEnums, FitnessWorkoutGenerateSoundBO> exerciseSoundMap = createGenerateSoundBO(languageList, exerciseTypeList);

            // 4. async generate and update task status
            CompletableFuture[] completableFutures = templateTaskBOList.stream().map(taskBO -> {
                return CompletableFuture.runAsync(() -> generateSingleAndUpdateStatus(taskBO, generateVideoBO, exerciseSoundMap.get(taskBO.getTemplate().getTemplateType().getExerciseType())), poolTaskExecutor);
            }).toArray(CompletableFuture[]::new);
            CompletableFuture.allOf(completableFutures);

        } catch (Exception e) {
            log.warn("Generate fitness workout failed.");
            log.warn(e.getMessage(), e);
            templateTasks.forEach(task -> task.setStatus(TemplateTaskStatusEnum.FAIL).setFailureMessage(StringUtils.substring(e.getMessage(), 0, 250)));
            templateTaskService.saveBatch(templateTasks);
        }
    }

    private List<String> getLanguageList(ProjInfo projInfo) {

        if (Objects.isNull(projInfo) || StringUtils.isBlank(projInfo.getLanguages())) {
            return Lists.newArrayList(GlobalConstant.DEFAULT_LANGUAGE);
        }

        // proj_info 可能不存在en配置
        List<String> languageList = Arrays.stream(projInfo.getLanguages().split(GlobalConstant.COMMA)).sorted().collect(Collectors.toList());
        if (!languageList.contains(GlobalConstant.DEFAULT_LANGUAGE)) {
            languageList.add(GlobalConstant.DEFAULT_LANGUAGE);
        }
        return languageList;
    }

    private void generateSingleAndUpdateStatus(FitnessTemplateTaskBO taskBO, FitnessWorkoutGenerateVideoBO generateVideoBO, FitnessWorkoutGenerateSoundBO generateSoundBO) {

        try {
            updateStatus4Task(taskBO.getTemplateTask(), TemplateTaskStatusEnum.RUNNING, null);
            generateWorkout(taskBO, generateVideoBO, generateSoundBO);
            success4Task(taskBO.getTemplateTask());
            log.debug("Generate succeed, templateId : {}", taskBO.getTemplate().getId());
        } catch (Exception e) {
            log.warn("Generate fitness workout failed.");
            log.warn(e.getMessage(), e);
            updateStatus4Task(taskBO.getTemplateTask(), TemplateTaskStatusEnum.FAIL, StringUtils.substring(e.getMessage(), 0, 250));
        }
    }

    private void generateWorkout(FitnessTemplateTaskBO templateTaskBO, FitnessWorkoutGenerateVideoBO generateVideoBO, FitnessWorkoutGenerateSoundBO generateSoundBO) {
        ProjFitnessTemplate fitnessTemplate = templateTaskBO.getTemplate();
        //v8.3.0根据  workout type 不同  wall pilate 选择 1-2 个, pilates & dumbbell 选择 1-3 个
        LinkedList<ManualPositionEnums> positionEnum4Main = randomSelectPosition(fitnessTemplate.getTemplateType());
        Map<ManualTypeEnums, ProjFitnessTemplateExerciseGroup> typeAndGroupMap = templateTaskBO.getTemplateGroupMap();
        //v8.3.0 workout type 为 106 时选择 dumbbell
        ManualEquipmentEnums equipmentEnums;
        if (!Objects.equals(TemplateTypeEnums.FITNESS_106, fitnessTemplate.getTemplateType()))
            equipmentEnums = ManualEquipmentEnums.NONE;
        else equipmentEnums = ManualEquipmentEnums.DUMBBELLS;
        List<ManualTargetEnums> targetEnumsList = Arrays.stream(ManualTargetEnums.values()).filter(target -> !Objects.equals(ManualTargetEnums.NONE, target)).collect(Collectors.toList());
        List<FitnessWorkoutGenerateBO> workoutGenerateList = Lists.newArrayListWithExpectedSize(fitnessTemplate.getDays() * targetEnumsList.size());

        ProjFitnessTemplateExerciseGroup warmUpGroup = typeAndGroupMap.get(ManualTypeEnums.WARM_UP);
        int warmUpDuration = computeDuration4Group(warmUpGroup);

        ProjFitnessTemplateExerciseGroup cooldownGroup = typeAndGroupMap.get(ManualTypeEnums.COOL_DOWN);
        int cooldownDuration = computeDuration4Group(cooldownGroup);
        ProjFitnessTemplateExerciseGroup mainGroup = typeAndGroupMap.get(ManualTypeEnums.MAIN);

        for (int day = 0; day < fitnessTemplate.getDays(); day++) {
            for (ManualTargetEnums targetEnum : targetEnumsList) {
                LinkedList<BaseGenerateVideoBO> mainVideos = selectVideo4Main(positionEnum4Main, warmUpDuration, cooldownDuration, fitnessTemplate, equipmentEnums, targetEnum, generateVideoBO, mainGroup);

                ManualPositionEnums positionEnums4Warmup = mainVideos.getFirst().getVideo().getPosition();
                LinkedList<ManualPositionEnums> positionEnum4Warmup = selectPosition4WarmUp(positionEnums4Warmup);
                List<BaseGenerateVideoBO> warmupVideos = selectVideo4WarmAndCoolDown(positionEnum4Warmup, fitnessTemplate, targetEnum, equipmentEnums, generateVideoBO, warmUpGroup);

                ManualPositionEnums positionEnums4Cooldown = mainVideos.getLast().getVideo().getPosition();
                LinkedList<ManualPositionEnums> positionEnum4Cooldown = selectPosition4CoolDown(positionEnums4Cooldown);
                List<BaseGenerateVideoBO> cooldownVideos = selectVideo4WarmAndCoolDown(positionEnum4Cooldown, fitnessTemplate, targetEnum, equipmentEnums, generateVideoBO, cooldownGroup);

                LinkedList<BaseGenerateVideoBO> selectedVideos = Lists.newLinkedList(warmupVideos);
                selectedVideos.addAll(mainVideos);
                selectedVideos.addAll(cooldownVideos);

                ProjFitnessWorkoutGenerate workoutGenerate = wrapWorkoutGenerate(templateTaskBO.getTemplateTask(), equipmentEnums, fitnessTemplate, targetEnum);
                FitnessWorkoutGenerateBO workoutGenerateBO = new FitnessWorkoutGenerateBO(selectedVideos, workoutGenerate, null);
                workoutGenerateList.add(workoutGenerateBO);
            }
        }

        List<String> languageList = templateTaskBO.getLanguageList();
        batchGenerateResource(workoutGenerateList, languageList, generateVideoBO, generateSoundBO);
        saveWorkoutAndRelation(workoutGenerateList);
    }

    private void saveWorkoutAndRelation(List<FitnessWorkoutGenerateBO> workoutGenerateList) {

        // workout
        List<ProjFitnessWorkoutGenerate> workoutList = workoutGenerateList.stream().map(FitnessWorkoutGenerateBO::getWorkoutGenerate).collect(Collectors.toList());
        this.saveBatch(workoutList);

        // relation
        List<ProjFitnessWorkoutGenerateExerciseVideo> relationList = workoutGenerateList.stream().map(generateBO -> {
            ProjFitnessWorkoutGenerate workout = generateBO.getWorkoutGenerate();
            return generateBO.getWorkoutAndRelationList().stream().map(
                    relation -> wrapGenerateExerciseVideo(workout, relation)).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
        generateVideoService.saveBatch(relationList);

        // workout i18n
        List<ProjFitnessWorkoutGenerateI18n> i18nWorkoutList = workoutGenerateList.stream().map(generateBO -> {
            ProjFitnessWorkoutGenerate workout = generateBO.getWorkoutGenerate();
            return generateBO.getI18nAudioJsonMap().entrySet().stream().map(audioEntry -> {
                ProjFitnessWorkoutGenerateI18n generateI18n = new ProjFitnessWorkoutGenerateI18n();
                generateI18n.setLanguage(audioEntry.getKey());
                generateI18n.setAudioJsonUrl(audioEntry.getValue());
                generateI18n.setProjFitnessWorkoutGenerateId(workout.getId());
                generateI18n.setProjId(workout.getProjId());
                return generateI18n;
            }).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
        workoutGenerateI18nService.saveBatch(i18nWorkoutList);
    }

    private ProjFitnessWorkoutGenerateExerciseVideo wrapGenerateExerciseVideo(ProjFitnessWorkoutGenerate workout,
                                                                              BaseGenerateVideoBO relation) {

        ProjFitnessWorkoutGenerateExerciseVideo workoutVideo = new ProjFitnessWorkoutGenerateExerciseVideo();
        workoutVideo.setProjFitnessTemplateId(workout.getProjFitnessTemplateId());
        workoutVideo.setProjFitnessTemplateTaskId(workout.getProjFitnessTemplateTaskId());
        workoutVideo.setProjFitnessWorkoutGenerateId(workout.getId());
        workoutVideo.setProjFitnessTemplateExerciseGroupId(relation.getGroupId());
        workoutVideo.setVideoDuration(relation.getVideoDuration());
        workoutVideo.setPreviewDuration(relation.getPreviewDuration());
        workoutVideo.setProjFitnessExerciseVideoId(relation.getVideo().getId());
        return workoutVideo;
    }

    private ProjFitnessWorkoutGenerate wrapWorkoutGenerate(ProjFitnessTemplateTask templateTask, ManualEquipmentEnums equipmentEnums,
                                                           ProjFitnessTemplate fitnessTemplate, ManualTargetEnums targetEnum) {

        ProjFitnessWorkoutGenerate workoutGenerate = new ProjFitnessWorkoutGenerate();
        workoutGenerate.setStatus(GlobalConstant.ONE);
        workoutGenerate.setProjId(fitnessTemplate.getProjId());
        workoutGenerate.setAudioLanguages(GlobalConstant.DEFAULT_LANGUAGE);
        workoutGenerate.setProjFitnessTemplateId(fitnessTemplate.getId());
        workoutGenerate.setProjFitnessTemplateTaskId(templateTask.getId());

        workoutGenerate.setWorkoutType(fitnessTemplate.getTemplateType().getExerciseType());
        workoutGenerate.setDifficulty(fitnessTemplate.getLevel());
        workoutGenerate.setSpecialLimit(fitnessTemplate.getSpecialLimit());

        workoutGenerate.setTarget(targetEnum);
        workoutGenerate.setEquipment(equipmentEnums);
        return workoutGenerate;
    }

    public void batchGenerateResource(List<FitnessWorkoutGenerateBO> workoutGenerateList, List<String> languageList,
                                      FitnessWorkoutGenerateVideoBO generateVideoBO, FitnessWorkoutGenerateSoundBO generateSound) {

        for (FitnessWorkoutGenerateBO workoutGenerateBO : workoutGenerateList) {
            BaseWorkoutBO baseWorkoutBO = generateBaseWorkout(workoutGenerateBO.getWorkoutAndRelationList(), languageList, true, generateVideoBO, generateSound);
            workoutGenerateBO.setI18nAudioJsonMap(baseWorkoutBO.getAudioI18nUrl());
            ProjFitnessWorkoutGenerate workoutGenerate = workoutGenerateBO.getWorkoutGenerate();
            workoutGenerate.setCalorie(baseWorkoutBO.getCalorie());
            workoutGenerate.setDuration(baseWorkoutBO.getDuration());
            workoutGenerate.setVideoUrl(baseWorkoutBO.getVideo2532Url());
            workoutGenerate.setAudioJsonUrl(baseWorkoutBO.getAudioJsonUrl());
        }
    }

    @Override
    public BaseWorkoutBO generateBaseWorkout(List<BaseGenerateVideoBO> workoutAndVideoList, List<String> languageList, boolean m3u8Upload,
                                             FitnessWorkoutGenerateVideoBO videoBO, FitnessWorkoutGenerateSoundBO soundBO) {

        if (CollectionUtils.isEmpty(workoutAndVideoList) || CollectionUtils.isEmpty(languageList)) {
            return null;
        }

        if (Objects.isNull(soundBO)) {
            ManualExerciseTypeEnums exerciseType = workoutAndVideoList.get(GlobalConstant.ZERO).getVideo().getExerciseType();
            soundBO = wrapExerciseSoundBO(exerciseType, languageList);
        }

        if (Objects.isNull(videoBO)) {
            Map<Integer, ProjFitnessExerciseVideo> videoIdMap = workoutAndVideoList.stream().collect(
                    Collectors.toMap(w -> w.getVideo().getId(), w -> w.getVideo(), (k1, k2) -> k1));
            Map<String, Map<Integer, ProjFitnessExerciseVideo>> videoI18nMap = listVideoI18nData(languageList, videoIdMap);
            videoBO = new FitnessWorkoutGenerateVideoBO();
            videoBO.setVideoI18nMap(videoI18nMap);
        }

        List<TsMergeBO> tsTextMerge2532BO = Lists.newLinkedList();
        Map<String, List<AudioJson104BO>> guidanceI18nMap = languageList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toMap(Function.identity(), lan -> Lists.newLinkedList()));

        int workoutDuration = GlobalConstant.ZERO;
        int relationLength = workoutAndVideoList.size();
        for (int i = 0; i < relationLength; i++) {

            BaseGenerateVideoBO relationBO = workoutAndVideoList.get(i);
            assembleM3u8Text(tsTextMerge2532BO, relationBO);

            boolean isLastNode = (i + 1 == relationLength);
            assembleAudioJson(guidanceI18nMap, i, soundBO, videoBO.getVideoI18nMap(), isLastNode, relationBO, workoutDuration);
            workoutDuration += relationBO.getVideoDuration();
        }

        BaseWorkoutBO workoutBO = new BaseWorkoutBO();
        uploadVideo(workoutBO, workoutDuration, m3u8Upload, workoutAndVideoList, tsTextMerge2532BO);
        uploadMultiAudio(guidanceI18nMap, workoutBO);

        return workoutBO;
    }

    private void uploadVideo(BaseWorkoutBO workoutBO, Integer workoutDuration,
                             boolean m3u8Upload, List<BaseGenerateVideoBO> workoutAndVideoList, List<TsMergeBO> tsTextMerge2532BO) {
        if (m3u8Upload) {
            BigDecimal workoutCalorie = computeVideoCalorie(workoutAndVideoList);
            workoutBO.setCalorie(workoutCalorie);
            workoutBO.setDuration(workoutDuration);
            UploadFileInfoRes m3u8FileInfoRes = fileService.uploadMergeTSForM3U8R2WithDiscontinuity(tsTextMerge2532BO, M3U8_DIR_KEY);
            workoutBO.setVideo2532Url(m3u8FileInfoRes.getFileRelativeUrl());
        }
    }

    private void uploadMultiAudio(Map<String, List<AudioJson104BO>> guidanceI18nMap,
                                  BaseWorkoutBO workoutBO) {

        Map<String, String> i18nAuduoMap = Maps.newHashMap();
        workoutBO.setAudioI18nUrl(i18nAuduoMap);
        guidanceI18nMap.entrySet().forEach(entry -> {
            UploadFileInfoRes fileInfoRes = fileService.uploadJsonR2(JacksonUtil.toJsonString(entry.getValue()), AUDIO_DIR_KEY);
            i18nAuduoMap.put(entry.getKey(), fileInfoRes.getFileRelativeUrl());
            if (Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, entry.getKey())) {
                workoutBO.setAudioJsonUrl(fileInfoRes.getFileRelativeUrl());
            }
        });
    }

    private BigDecimal computeVideoCalorie(List<BaseGenerateVideoBO> relationBOList) {

        Double workoutCalorie = relationBOList.stream().mapToDouble(relation -> {
            return (relation.getPreviewRound() + relation.getVideoRound()) * relation.getVideo().getCalorie().doubleValue();
        }).sum();

        return BigDecimal.valueOf(workoutCalorie);
    }

    private Map<ManualExerciseTypeEnums, FitnessWorkoutGenerateSoundBO> createGenerateSoundBO(List<String> languageList, Set<ManualExerciseTypeEnums> exerciseTypeList) {

        Map<ManualExerciseTypeEnums, FitnessWorkoutGenerateSoundBO> exerciseAndSoundMap = Maps.newHashMap();
        exerciseTypeList.forEach(exerciseType -> {
            exerciseAndSoundMap.put(exerciseType, wrapExerciseSoundBO(exerciseType, languageList));
        });
        return exerciseAndSoundMap;
    }

    private FitnessWorkoutGenerateSoundBO wrapExerciseSoundBO(ManualExerciseTypeEnums exerciseType, List<String> languageList) {

        FitnessSoundConfig soundConfig = fetchConfigWithExerciseType(exerciseType);
        FitnessWorkoutGenerateSoundBO soundBO = new FitnessWorkoutGenerateSoundBO();
        AudioJson104BO firstAudio = soundBO.getSoundByName(AudioCategoryEnums.FIRST, soundConfig.getFirst());
        AudioJson104BO readyAudio = soundBO.getSoundByName(AudioCategoryEnums.NEXT, soundConfig.getReadyFor());
        AudioJson104BO threeTwoOneAudio = soundBO.getSoundByName(AudioCategoryEnums.THREE_TWO_ONE, soundConfig.getThreeTwoOne());

        soundBO.setFirst(firstAudio);
        soundBO.setGetReady(readyAudio);
        soundBO.setThreeTwoOne(threeTwoOneAudio);

        AudioJson104BO goAudio = soundBO.getSoundByName(AudioCategoryEnums.GO, soundConfig.getGo());
        AudioJson104BO lastAudio = soundBO.getSoundByName(AudioCategoryEnums.LAST, soundConfig.getLast());
        List<AudioJson104BO> promptList = soundBO.getAudioList(soundConfig.getPromptList(), AudioCategoryEnums.PROMPT);

        soundBO.setGo(goAudio);
        soundBO.setLast(lastAudio);
        soundBO.setPromptList(promptList);

        Map<String, FitnessWorkoutGenerateSoundBO> soundBOMap = Maps.newHashMap();
        soundBOMap.put(GlobalConstant.DEFAULT_LANGUAGE, soundBO);
        FitnessWorkoutGenerateSoundBO i18nSoundBO = new FitnessWorkoutGenerateSoundBO();
        i18nSoundBO.setSoundI18nMap(soundBOMap);

        Set<String> translationLanguages = languageList.stream().filter(StringUtils::isNotBlank).filter(language -> !Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(translationLanguages)) {
            return i18nSoundBO;
        }

        List<AudioJson104BO> audioList = Lists.newArrayList(firstAudio, readyAudio, threeTwoOneAudio, goAudio, lastAudio);
        audioList.addAll(promptList);
        Map<Integer, Map<String, AudioJson104BO>> i18nSoundMap = getSoundI18n(audioList, translationLanguages);
        translationLanguages.forEach(language -> {

            AudioJson104BO i18nFirstAudio = i18nSoundMap.get(firstAudio.getSoundId()).get(language);
            AudioJson104BO i18nReadyAudio = i18nSoundMap.get(readyAudio.getSoundId()).get(language);
            AudioJson104BO i18nLastAudio = i18nSoundMap.get(lastAudio.getSoundId()).get(language);

            AudioJson104BO i18nGoAudio = i18nSoundMap.get(goAudio.getSoundId()).get(language);
            AudioJson104BO i18n321Audio = i18nSoundMap.get(threeTwoOneAudio.getSoundId()).get(language);

            List<AudioJson104BO> i18nPromptAudioList = promptList.stream().map(audio -> i18nSoundMap.get(audio.getSoundId()).get(language)).collect(Collectors.toList());
            FitnessWorkoutGenerateSoundBO languageSoundBO = new FitnessWorkoutGenerateSoundBO().setFirst(i18nFirstAudio).setGetReady(i18nReadyAudio)
                    .setLast(i18nLastAudio).setGo(i18nGoAudio).setThreeTwoOne(i18n321Audio).setPromptList(i18nPromptAudioList);
            soundBOMap.put(language, languageSoundBO);
        });

        return i18nSoundBO;
    }

    private Map<Integer, Map<String, AudioJson104BO>> getSoundI18n(List<AudioJson104BO> soundList, Collection<String> languages) {

        if (CollectionUtils.isEmpty(soundList) || CollectionUtils.isEmpty(languages)) {
            return Maps.newHashMap();
        }

        List<ProjFitnessSoundI18n> i18nModelList = soundList.stream().filter(AudioJson104BO::getNeedTranslation).map(ProjFitnessSoundI18n::new).collect(Collectors.toList());
        Map<Object, Map<LanguageEnums, AudioTranslateResultModel>> soundI18nMap = speechI18nPubService. getI18nResultGroupByKey(
                i18nModelList, languages, ProjCodeEnums.OOG104);
        Map<Integer, Map<String, AudioJson104BO>> audioJson104I18nMap = new HashMap<>();
        for (AudioJson104BO audioJson104BO : soundList) {

            Boolean needTranslation = audioJson104BO.getNeedTranslation();
            Map<LanguageEnums, AudioTranslateResultModel> langSoundMap = soundI18nMap.get(audioJson104BO.getSoundId());
            if (needTranslation && langSoundMap == null) {
                throw new BizException("The System sound translation incomplete, sound id is : " + audioJson104BO.getSoundId());
            }

            Map<String, AudioJson104BO> fitnessSoundI18nMap = new HashMap<>();
            for (String language : languages) {

                if (!needTranslation) {
                    fitnessSoundI18nMap.put(language, audioJson104BO);
                    continue;
                }

                AudioTranslateResultModel fitnessSoundI18n = langSoundMap.get(LanguageEnums.getByNameIgnoreCase(language));
                if (Objects.isNull(fitnessSoundI18n)) {
                    throw new BizException("The System sound translation incomplete, sound id is : " + audioJson104BO.getSoundId());
                }

                AudioJson104BO json104BO = new AudioJson104BO(
                        audioJson104BO.getCategory(),
                        audioJson104BO.getId(),
                        fileService.getAbsoluteR2Url(fitnessSoundI18n.getAudioUrl()),
                        FireBaseUrlSubUtils.getFileName(fitnessSoundI18n.getAudioUrl()),
                        audioJson104BO.getTime(),
                        fitnessSoundI18n.getDuration(),
                        false, audioJson104BO.getSoundId(),
                        false, audioJson104BO.getCoreVoiceConfigI18nId(),
                        fitnessSoundI18n.getText(),audioJson104BO.getGender());

                fitnessSoundI18nMap.put(language, json104BO);
            }

            audioJson104I18nMap.put(audioJson104BO.getSoundId(), fitnessSoundI18nMap);
        }

        return audioJson104I18nMap;
    }

    private FitnessSoundConfig fetchConfigWithExerciseType(ManualExerciseTypeEnums exerciseType) {

        if (Objects.equals(ManualExerciseTypeEnums.REGULAR_FITNESS, exerciseType)) {
            return oog104BizConfig.getRegularFitnessSoundConfig();
        } else if (Objects.equals(ManualExerciseTypeEnums.WALL_PILATES, exerciseType)) {
            return oog104BizConfig.getWallPilatesSoundConfig();
        } else if (Objects.equals(ManualExerciseTypeEnums.CLASSIC_YOGA, exerciseType)) {
            return oog104BizConfig.getClassYogaSoundConfig();
        } else if (Objects.equals(ManualExerciseTypeEnums.CHAIR_YOGA, exerciseType)) {
            return oog104BizConfig.getChairYogaSoundConfig();
        } else if (Objects.equals(ManualExerciseTypeEnums.FITNESS_106, exerciseType)) {
            return oog104BizConfig.get106FitnessSoundConfig();
        } else if (Objects.equals(ManualExerciseTypeEnums.PILATES, exerciseType)) {
            return oog104BizConfig.getPilatesSoundConfig();
        } else {
            return oog104BizConfig.getRegularFitnessSoundConfig();
        }
    }

    private void assembleAudioJson(Map<String, List<AudioJson104BO>> guidanceI18nMap, int videoIndex,
                                   FitnessWorkoutGenerateSoundBO i18nSoundBO, Map<String, Map<Integer, ProjFitnessExerciseVideo>> i18nVideoMap,
                                   boolean lastBode, BaseGenerateVideoBO relationBO, int workoutDuration) {

        boolean firstNode = workoutDuration == GlobalConstant.ZERO;
        guidanceI18nMap.entrySet().forEach(entry -> {

            String language = entry.getKey();
            ProjFitnessExerciseVideo originalVideo = relationBO.getVideo();
            FitnessWorkoutGenerateSoundBO soundBO = i18nSoundBO.getSoundI18nMap().get(language);
            ProjFitnessExerciseVideo i18nVideo = i18nVideoMap.get(language).get(originalVideo.getId());

            List<AudioJson104BO> guidanceAudioList = entry.getValue();
            int previewDuration4Video = workoutDuration;
            int playTime = previewDuration4Video + GlobalConstant.HUNDRED;
            for (int i = 0; i < relationBO.getPreviewRound(); i++) {

                // 目前第一轮Preview才有音频
                if (i > 0) {
                    previewDuration4Video += originalVideo.getFrontVideoDuration();
                    continue;
                }

                if (firstNode) {
                    AudioJson104BO firstAudio = soundBO.getFirst();
                    addAudioJson(guidanceAudioList, firstAudio, playTime);
                    playTime += firstAudio.getDuration().intValue();
                }

                if (!firstNode) {
                    AudioJson104BO playAudio = lastBode ? soundBO.getLast() : soundBO.getGetReady();
                    addAudioJson(guidanceAudioList, playAudio, playTime);
                    playTime += playAudio.getDuration().intValue();
                }

                playTime += GlobalConstant.THOUSAND;
                AudioJson104BO nameAudio = new AudioJson104BO(AudioCategoryEnums.NAME, FireBaseUrlSubUtils.getFileName(i18nVideo.getNameAudioUrl()),
                        fileService.getAbsoluteR2Url(i18nVideo.getNameAudioUrl()), FireBaseUrlSubUtils.getFileName(i18nVideo.getNameAudioUrl()),
                        BigDecimal.ZERO, i18nVideo.getNameAudioDuration(), false);
                addAudioJson(guidanceAudioList, nameAudio, playTime);

                AudioJson104BO play321Audio = soundBO.getThreeTwoOne();
                playTime = previewDuration4Video + originalVideo.getFrontVideoDuration() - play321Audio.getDuration();
                addAudioJson(guidanceAudioList, play321Audio, playTime);
                previewDuration4Video += originalVideo.getFrontVideoDuration();
            }

            playTime = previewDuration4Video + GlobalConstant.HUNDRED;
            AudioJson104BO goAudio = soundBO.getGo();
            addAudioJson(guidanceAudioList, goAudio, playTime);

            // guidance
            playTime = previewDuration4Video + GlobalConstant.THOUSAND;
            AudioJson104BO guidanceAudio = new AudioJson104BO(AudioCategoryEnums.GUIDANCE, FireBaseUrlSubUtils.getFileName(i18nVideo.getGuidanceAudioUrl()),
                    fileService.getAbsoluteR2Url(i18nVideo.getGuidanceAudioUrl()), FireBaseUrlSubUtils.getFileName(i18nVideo.getGuidanceAudioUrl()), BigDecimal.ZERO, i18nVideo.getGuidanceAudioDuration(), true);
            addAudioJson(guidanceAudioList, guidanceAudio, playTime);

            // exercise duration
            int exerciseDuration4Video = GlobalConstant.ZERO;
            for (int i = 0; i < relationBO.getVideoRound(); i++) {
                if (i % 2 == 0) {
                    exerciseDuration4Video += originalVideo.getFrontVideoDuration();
                } else {
                    exerciseDuration4Video += originalVideo.getSideVideoDuration();
                }
            }

            // prompt
            if ((videoIndex + 1) % 3 == 0) {
                playTime = previewDuration4Video + exerciseDuration4Video / 2;
                AudioJson104BO randomAudio = randomSelectOne(soundBO.getPromptList());
                addAudioJson(guidanceAudioList, randomAudio, playTime);
            }

            // end
            playTime = previewDuration4Video + exerciseDuration4Video - soundBO.getThreeTwoOne().getDuration();
            addAudioJson(guidanceAudioList, soundBO.getThreeTwoOne(), playTime);
        });

    }

    private AudioJson104BO randomSelectOne(List<AudioJson104BO> audioList) {

        Collections.shuffle(audioList);
        return audioList.get(0);
    }

    private void addAudioJson(List<AudioJson104BO> guidanceAudioList, AudioJson104BO audio, int playtime) {

        BigDecimal time = new BigDecimal(playtime).divide(new BigDecimal(SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP);
        AudioJson104BO newAudio = new AudioJson104BO(audio.getCategory(), audio.getId(), audio.getUrl(), audio.getName(), time, audio.getDuration(), audio.isClose());
        guidanceAudioList.add(newAudio);
    }

    private void assembleM3u8Text(List<TsMergeBO> tsTextMerge2532BO,
                                  BaseGenerateVideoBO relationBO) {

        List<TsMergeBO> videoTsList = Lists.newArrayList();
        AtomicInteger videoDuration = new AtomicInteger(GlobalConstant.ZERO);
        ProjFitnessExerciseVideo exerciseVideo = relationBO.getVideo();
        Consumer<Integer> m3u8Consumer = index -> {
            // 从正开始
            if (index % 2 == 0) {
                videoTsList.add(new TsMergeBO(fileService.getAbsoluteUrl(exerciseVideo.getFrontVideoUrl()), exerciseVideo.getFrontVideoDuration()));
                videoDuration.addAndGet(exerciseVideo.getFrontVideoDuration());
            } else {
                videoTsList.add(new TsMergeBO(fileService.getAbsoluteUrl(exerciseVideo.getSideVideoUrl()), exerciseVideo.getSideVideoDuration()));
                videoDuration.addAndGet(exerciseVideo.getSideVideoDuration());
            }
        };

        for (int i = 0; i < relationBO.getPreviewRound(); i++) {
            m3u8Consumer.accept(i);
        }
        relationBO.setPreviewDuration(videoDuration.get());

        for (int i = 0; i < relationBO.getVideoRound(); i++) {
            m3u8Consumer.accept(i);
        }
        relationBO.setVideoDuration(videoDuration.get());

        tsTextMerge2532BO.addAll(videoTsList);
    }

    private int computeDuration4Group(ProjFitnessTemplateExerciseGroup exerciseGroup) {

        return exerciseGroup.getCount() * (exerciseGroup.getRounds() + PREVIEW_ROUND) * VIDEO_DURATION;
    }

    private List<BaseGenerateVideoBO> selectVideo4WarmAndCoolDown(LinkedList<ManualPositionEnums> positionEnumList,
                                                                  ProjFitnessTemplate fitnessTemplate,
                                                                  ManualTargetEnums targetEnum,
                                                                  ManualEquipmentEnums equipmentEnums,
                                                                  FitnessWorkoutGenerateVideoBO generateVideoBO,
                                                                  ProjFitnessTemplateExerciseGroup exerciseGroup) {

        ManualExerciseTypeEnums exerciseType = fitnessTemplate.getTemplateType().getExerciseType();
        List<ExerciseVideoSpecialLimitEnums> specialLimitList = fitnessTemplate.getSpecialLimit();
        LinkedList<ProjFitnessExerciseVideo> matchVideos = generateVideoBO.listByPriority(exerciseGroup.getGroupType(), exerciseType, positionEnumList, equipmentEnums, specialLimitList, null, null, targetEnum);
        List<ProjFitnessExerciseVideo> exerciseVideoList = selectVideo4Count(matchVideos, exerciseGroup.getCount(), generateVideoBO.getVideoIdMap());

        if (!Objects.equals(exerciseVideoList.size(), exerciseGroup.getCount())) {
            String errorMessage = String.format("Video count less than template require, template group info : %s .", JacksonUtil.toJsonString(exerciseGroup));
            log.warn(errorMessage);
            throw new BizException(errorMessage);
        }

        List<BaseGenerateVideoBO> videoList4Relation = exerciseVideoList.stream().sorted(Comparator.comparing(video -> video.getPosition().getCode()))
                .map(video -> {
                    return new BaseGenerateVideoBO(exerciseGroup.getRounds(), exerciseGroup.getId(), video);
                }).collect(Collectors.toList());

        return videoList4Relation;
    }

    private List<ProjFitnessExerciseVideo> selectVideo4Count(List<ProjFitnessExerciseVideo> videoList,
                                                             Integer requireCount,
                                                             Map<Integer, ProjFitnessExerciseVideo> videoIdMap) {

        int matchCount = videoList.stream().mapToInt(video -> Objects.equals(video.getVideoDirection(), ManualVideoDirectionEnums.CENTRAL) ? 1 : 2).sum();
        int videoCount = requireCount;
        if (matchCount < videoCount) {
            return Collections.emptyList();
        }

        List<ProjFitnessExerciseVideo> selectedVideoList = Lists.newLinkedList();
        for (ProjFitnessExerciseVideo video : videoList) {
            if (videoCount <= 0) {
                break;
            }

            if (Objects.equals(video.getVideoDirection(), ManualVideoDirectionEnums.LEFT)) {
                if (videoCount < 2) {
                    Optional<ProjFitnessExerciseVideo> centralVideo = selectedVideoList.stream().filter(exerciseVideo ->
                            Objects.equals(exerciseVideo.getVideoDirection(), ManualVideoDirectionEnums.CENTRAL)).findAny();
                    if (!centralVideo.isPresent()) {
                        continue;
                    }
                    selectedVideoList.remove(centralVideo.get());
                    videoCount += 1;
                }
                selectedVideoList.add(video);
                selectedVideoList.add(videoIdMap.get(video.getLeftRightVideoId()));
                videoCount -= 2;
            }

            if (Objects.equals(video.getVideoDirection(), ManualVideoDirectionEnums.CENTRAL)) {
                selectedVideoList.add(video);
                videoCount -= 1;
            }
        }
        return selectedVideoList;
    }


    private LinkedList<BaseGenerateVideoBO> selectVideo4Main(LinkedList<ManualPositionEnums> positionEnumList,
                                                             int warmupDuration,
                                                             int cooldownDuration,
                                                             ProjFitnessTemplate fitnessTemplate,
                                                             ManualEquipmentEnums equipmentEnums,
                                                             ManualTargetEnums targetEnum,
                                                             FitnessWorkoutGenerateVideoBO generateVideoBO,
                                                             ProjFitnessTemplateExerciseGroup group) {

        WorkoutDurationRangeEnums durationRange = fitnessTemplate.getDurationRange();
        int minDuration = durationRange.getMin() - warmupDuration - cooldownDuration;
        int maxDuration = durationRange.getMax() - warmupDuration - cooldownDuration;
        int mainCount = randomCount4Main(minDuration, maxDuration, PREVIEW_ROUND, group.getRounds());

        ManualExerciseTypeEnums exerciseType = fitnessTemplate.getTemplateType().getExerciseType();
        List<ExerciseVideoSpecialLimitEnums> specialLimitList = fitnessTemplate.getSpecialLimit();
        ManualTypeEnums videoType = group.getGroupType();

        LinkedList<DifficultyAndIntensityMappingBO> difficultyBOList = Lists.newLinkedList(DifficultyAndIntensityMappingBO.getMappingByDifficulty(fitnessTemplate.getLevel()));
        LinkedList<TargetMappingBO> targetMappingBOList = Lists.newLinkedList(TargetMappingBO.getTargetMapping(targetEnum));
        LinkedList<EquipmentMappingBO> equipmentMappingBOS = Lists.newLinkedList(EquipmentMappingBO.getEquipmentMappingByWorkoutDurationRange(fitnessTemplate.getDurationRange()));
        List<ProjFitnessExerciseVideo> selectedVideoList = Lists.newLinkedList();
        int combineCount = GlobalConstant.ZERO;
        //v8.3.0优先级更改,需要判断 template type
        if (Objects.equals(exerciseType, ManualExerciseTypeEnums.WALL_PILATES) ||
                Objects.equals(exerciseType, ManualExerciseTypeEnums.FITNESS_106) ||
                Objects.equals(exerciseType, ManualExerciseTypeEnums.PILATES)) {
            if (Objects.equals(exerciseType, ManualExerciseTypeEnums.FITNESS_106)) {
                for (EquipmentMappingBO equipmentMapping : equipmentMappingBOS) {
                    equipmentEnums = equipmentMapping.getEquipment();
                    int equipmentCount;
                    if (Objects.equals(equipmentMapping, equipmentMappingBOS.getLast())) {
                        equipmentCount = mainCount - combineCount;
                    } else {
                        equipmentCount = Math.round(mainCount * equipmentMapping.getPercent());
                    }

                    if (equipmentCount < 1) {
                        continue;
                    } else {
                        combineCount += equipmentCount;
                    }

                    for (TargetMappingBO targetMapping : targetMappingBOList) {

                        int targetCount;
                        if (Objects.equals(targetMapping, targetMappingBOList.getLast())) {
                            targetCount = equipmentCount;
                        } else {
                            targetCount = Math.round(equipmentCount * targetMapping.getPercent());
                        }

                        if (targetCount < 1) {
                            continue;
                        } else {
                            equipmentCount -= targetCount;
                        }
                        for (DifficultyAndIntensityMappingBO difficultyMapping : difficultyBOList) {
                            int difficultyCount;
                            if (Objects.equals(difficultyMapping, difficultyBOList.getLast())) {
                                difficultyCount = targetCount;
                            } else {
                                difficultyCount = Math.round(targetCount * difficultyMapping.getPercent());
                            }

                            if (difficultyCount < 1) {
                                continue;
                            } else {
                                targetCount -= difficultyCount;
                            }
                            List<ProjFitnessExerciseVideo> videoList = listVideo4Main(videoType, exerciseType, positionEnumList, equipmentEnums, specialLimitList,
                                    difficultyMapping.getDifficultyEnums(), difficultyMapping.getIntensityEnums(), targetMapping.getTarget(), generateVideoBO, selectedVideoList);
                            List<ProjFitnessExerciseVideo> matchVideoList = selectVideo4Count(videoList, difficultyCount, generateVideoBO.getVideoIdMap());
                            if (!Objects.equals(matchVideoList.size(), difficultyCount)) {
                                String errorMessage = String.format("No enough main video, mapping is %s, exercise type is %s, position is %s, specialLimits is %s, difficulty is %s, target is %s.",
                                        JacksonUtil.toJsonString(difficultyMapping), exerciseType, positionEnumList, specialLimitList, fitnessTemplate.getLevel(), targetEnum);
                                log.warn(errorMessage);
                                throw new BizException(errorMessage);
                            }
                            selectedVideoList.addAll(matchVideoList);
                        }
                    }
                }
            } else {
                //不是 106 的不考虑器材分配
                for (TargetMappingBO targetMapping : targetMappingBOList) {
                    int targetCount;
                    if (Objects.equals(targetMapping, targetMappingBOList.getLast())) {
                        targetCount = mainCount - combineCount;
                        ;
                    } else {
                        targetCount = Math.round(mainCount * targetMapping.getPercent());
                    }

                    if (targetCount < 1) {
                        continue;
                    } else {
                        combineCount += targetCount;
                    }
                    for (DifficultyAndIntensityMappingBO difficultyMapping : difficultyBOList) {
                        int difficultyCount;
                        if (Objects.equals(difficultyMapping, difficultyBOList.getLast())) {
                            difficultyCount = targetCount;
                        } else {
                            difficultyCount = Math.round(targetCount * difficultyMapping.getPercent());
                        }

                        if (difficultyCount < 1) {
                            continue;
                        } else {
                            targetCount -= difficultyCount;
                        }
                        List<ProjFitnessExerciseVideo> videoList = listVideo4Main(videoType, exerciseType, positionEnumList, equipmentEnums, specialLimitList,
                                difficultyMapping.getDifficultyEnums(), difficultyMapping.getIntensityEnums(), targetMapping.getTarget(), generateVideoBO, selectedVideoList);
                        List<ProjFitnessExerciseVideo> matchVideoList = selectVideo4Count(videoList, difficultyCount, generateVideoBO.getVideoIdMap());
                        if (!Objects.equals(matchVideoList.size(), difficultyCount)) {
                            String errorMessage = String.format("No enough main video, mapping is %s, exercise type is %s, position is %s, specialLimits is %s, difficulty is %s, target is %s.",
                                    JacksonUtil.toJsonString(difficultyMapping), exerciseType, positionEnumList, specialLimitList, fitnessTemplate.getLevel(), targetEnum);
                            log.warn(errorMessage);
                            throw new BizException(errorMessage);
                        }
                        selectedVideoList.addAll(matchVideoList);
                    }
                }
            }
        } else {
            //非 v8.3.0按照原有逻辑
            for (DifficultyAndIntensityMappingBO difficultyMapping : difficultyBOList) {
                int difficultyCount;
                if (Objects.equals(difficultyMapping, difficultyBOList.getLast())) {
                    difficultyCount = mainCount - combineCount;
                } else {
                    difficultyCount = Math.round(mainCount * difficultyMapping.getPercent());
                }

                if (difficultyCount < 1) {
                    continue;
                } else {
                    combineCount += difficultyCount;
                }

                for (TargetMappingBO targetMapping : targetMappingBOList) {

                    int targetCount;
                    if (Objects.equals(targetMapping, targetMappingBOList.getLast())) {
                        targetCount = difficultyCount;
                    } else {
                        targetCount = Math.round(difficultyCount * targetMapping.getPercent());
                    }

                    if (targetCount < 1) {
                        continue;
                    } else {
                        difficultyCount -= targetCount;
                    }
                    List<ProjFitnessExerciseVideo> videoList = listVideo4Main(videoType, exerciseType, positionEnumList, equipmentEnums, specialLimitList,
                            difficultyMapping.getDifficultyEnums(), difficultyMapping.getIntensityEnums(), targetMapping.getTarget(), generateVideoBO, selectedVideoList);
                    List<ProjFitnessExerciseVideo> matchVideoList = selectVideo4Count(videoList, targetCount, generateVideoBO.getVideoIdMap());
                    if (!Objects.equals(matchVideoList.size(), targetCount)) {
                        String errorMessage = String.format("No enough main video, mapping is %s, exercise type is %s, position is %s, specialLimits is %s, difficulty is %s, target is %s.",
                                JacksonUtil.toJsonString(difficultyMapping), exerciseType, positionEnumList, specialLimitList, fitnessTemplate.getLevel(), targetEnum);
                        log.warn(errorMessage);
                        throw new BizException(errorMessage);
                    }
                    selectedVideoList.addAll(matchVideoList);
                }
            }
        }
        //先按照器材倒序排列再按照 position 排序
        LinkedList<BaseGenerateVideoBO> videoBOList = selectedVideoList.stream().sorted(Comparator.comparing((ProjFitnessExerciseVideo video) -> video.getEquipment().getCode())
                        .reversed()
                        .thenComparing(video -> video.getPosition().getCode()))
                .map(video -> new BaseGenerateVideoBO(group.getRounds(), group.getId(), video))
                .collect(Collectors.toCollection(LinkedList::new));
        return videoBOList;
    }

    private List<ProjFitnessExerciseVideo> listVideo4Main(ManualTypeEnums videoType,
                                                          ManualExerciseTypeEnums exerciseType,
                                                          List<ManualPositionEnums> positionEnumList,
                                                          ManualEquipmentEnums equipmentEnums,
                                                          List<ExerciseVideoSpecialLimitEnums> specialLimitList,
                                                          List<ManualDifficultyEnums> difficulty,
                                                          List<ManualIntensityEnums> intensity,
                                                          ManualTargetEnums target,
                                                          FitnessWorkoutGenerateVideoBO generateVideoBO,
                                                          List<ProjFitnessExerciseVideo> selectedVideoList) {

        LinkedList<ProjFitnessExerciseVideo> videoList = generateVideoBO.listByPriority(videoType, exerciseType, positionEnumList, equipmentEnums, specialLimitList, difficulty, intensity, target);
        return videoList.stream().filter(video -> !selectedVideoList.contains(video)).collect(Collectors.toList());
    }

    private int randomCount4Main(int minDuration, int maxDuration, int previewRound, int videoRound) {

        int minCount = (minDuration / (VIDEO_DURATION * (previewRound + videoRound))) + 1;
        int maxCount = (maxDuration / (VIDEO_DURATION * (previewRound + videoRound)));

        Random random = new Random();
        int count = random.nextInt(maxCount - minCount) + minCount;
        return count;
    }

    private LinkedList<ManualPositionEnums> selectPosition4WarmUp(ManualPositionEnums positionEnums4Main) {

        List<ManualPositionEnums> allPositionEnumList = Arrays.stream(ManualPositionEnums.values()).collect(Collectors.toList());
        LinkedList<ManualPositionEnums> positionEnums = allPositionEnumList.stream().filter(position -> position.getCode() <= positionEnums4Main.getCode()).collect(Collectors.toCollection(LinkedList::new));
        Collections.shuffle(positionEnums);
        return positionEnums;
    }

    private LinkedList<ManualPositionEnums> selectPosition4CoolDown(ManualPositionEnums positionEnums4Main) {

        List<ManualPositionEnums> allPositionEnumList = Arrays.stream(ManualPositionEnums.values()).collect(Collectors.toList());
        LinkedList<ManualPositionEnums> positionEnums = allPositionEnumList.stream().filter(position -> position.getCode() >= positionEnums4Main.getCode()).collect(Collectors.toCollection(LinkedList::new));
        Collections.shuffle(positionEnums);
        return positionEnums;
    }

    private LinkedList<ManualPositionEnums> randomSelectPosition(TemplateTypeEnums typeEnums) {
        //根据 type , mian 选择不同的 position
        Random random = new Random();
        int positionCount;
        if (Objects.equals(typeEnums, TemplateTypeEnums.WALL_PILATES))
            positionCount = random.nextInt(2) + 1;
        else
            positionCount = random.nextInt(3) + 1;
        List<ManualPositionEnums> positionEnumsList = Arrays.stream(ManualPositionEnums.values()).collect(Collectors.toList());
        Collections.shuffle(positionEnumsList);

        positionEnumsList = positionEnumsList.subList(0, positionCount);
        positionEnumsList.sort(Comparator.comparing(ManualPositionEnums::getCode));
        return Lists.newLinkedList(positionEnumsList);
    }

    private void updateStatus4Task(ProjFitnessTemplateTask templateTask, TemplateTaskStatusEnum status, String failMessage) {

        templateTask.setStatus(status);
        templateTask.setFailureMessage(failMessage);
        templateTaskService.updateById(templateTask);
    }

    private void success4Task(ProjFitnessTemplateTask templateTask) {

        templateTask.setStatus(TemplateTaskStatusEnum.SUCCESS);
        templateTaskService.updateById(templateTask);

        if (templateTask.getCleanUp() == GlobalConstant.ONE) {

            LambdaQueryWrapper<ProjFitnessWorkoutGenerate> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjFitnessWorkoutGenerate::getProjFitnessTemplateId, templateTask.getId());
            queryWrapper.ne(ProjFitnessWorkoutGenerate::getProjFitnessTemplateTaskId, templateTask.getId());

            List<ProjFitnessWorkoutGenerate> generateList = list(queryWrapper);
            if (CollectionUtils.isEmpty(generateList)) {
                return;
            }

            List<Integer> generateIds = generateList.stream().map(ProjFitnessWorkoutGenerate::getId).collect(Collectors.toList());
            LambdaUpdateWrapper<ProjFitnessWorkoutGenerate> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ProjFitnessWorkoutGenerate::getDelFlag, GlobalConstant.ONE);
            updateWrapper.set(ProjFitnessWorkoutGenerate::getUpdateTime, LocalDateTime.now());
            updateWrapper.in(ProjFitnessWorkoutGenerate::getId, generateIds);
            update(updateWrapper);

            LambdaUpdateWrapper<ProjFitnessWorkoutGenerateExerciseVideo> relationUpdateWrapper = new LambdaUpdateWrapper<>();
            relationUpdateWrapper.set(ProjFitnessWorkoutGenerateExerciseVideo::getDelFlag, GlobalConstant.ONE);
            relationUpdateWrapper.set(ProjFitnessWorkoutGenerateExerciseVideo::getUpdateTime, LocalDateTime.now());
            relationUpdateWrapper.eq(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessTemplateId, templateTask.getProjFitnessTemplateId());
            relationUpdateWrapper.ne(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessTemplateTaskId, templateTask.getId());
            generateVideoService.update(relationUpdateWrapper);

            LambdaUpdateWrapper<ProjFitnessWorkoutGenerateI18n> i18nWrapper = new LambdaUpdateWrapper<>();
            i18nWrapper.set(ProjFitnessWorkoutGenerateI18n::getDelFlag, GlobalConstant.ONE);
            i18nWrapper.set(ProjFitnessWorkoutGenerateI18n::getUpdateTime, LocalDateTime.now());
            i18nWrapper.in(ProjFitnessWorkoutGenerateI18n::getId, generateIds);
            workoutGenerateI18nService.update(i18nWrapper);
        }
    }

    private FitnessWorkoutGenerateVideoBO createWorkoutGenerateVideoBO(List<String> languageList) {

        FitnessWorkoutGenerateVideoBO workoutGenerateBO = new FitnessWorkoutGenerateVideoBO();
        List<ProjFitnessExerciseVideo> allVideos = videoService.listEnable4AudoGenerate();
        List<ProjFitnessExerciseVideo> validVideoList = allVideos.stream()
                .filter(video -> Objects.nonNull(video.getFrontVideoDuration()))
                .filter(video -> Objects.nonNull(video.getSideVideoDuration()))
                .collect(Collectors.toList());

        Map<Integer, ProjFitnessExerciseVideo> videoIdMap = validVideoList.stream().collect(Collectors.toMap(ProjFitnessExerciseVideo::getId, Function.identity()));
        workoutGenerateBO.setVideoIdMap(videoIdMap);

        List<ProjFitnessExerciseVideo> centralAndLeftVideos = validVideoList.stream()
                .filter(video -> {
                    if (Objects.equals(video.getVideoDirection(), ManualVideoDirectionEnums.CENTRAL)) {
                        return true;
                    }
                    if (Objects.equals(video.getVideoDirection(), ManualVideoDirectionEnums.LEFT) && Objects.nonNull(video.getLeftRightVideoId())) {
                        return videoIdMap.containsKey(video.getLeftRightVideoId());
                    }
                    return false;
                })
                .collect(Collectors.toList());
        workoutGenerateBO.setCentralAndLeftVideoList(centralAndLeftVideos);

        Map<String, Map<Integer, ProjFitnessExerciseVideo>> videoI18nMap = listVideoI18nData(languageList, videoIdMap);
        workoutGenerateBO.setVideoI18nMap(videoI18nMap);

        Map<ExerciseVideoSpecialLimitEnums, Set<ProjFitnessExerciseVideo>> videoLimitMap = Maps.newHashMap();
        workoutGenerateBO.setVideoLimitMap(videoLimitMap);
        Map<ManualTargetEnums, Set<ProjFitnessExerciseVideo>> videoTargetMap = Maps.newHashMap();
        workoutGenerateBO.setVideoTargetMap(videoTargetMap);

        for (ProjFitnessExerciseVideo video : centralAndLeftVideos) {

            video.getSpecialLimit().forEach(specialLimitEnum -> {
                if (videoLimitMap.containsKey(specialLimitEnum)) {
                    videoLimitMap.get(specialLimitEnum).add(video);
                    return;
                }

                Set videoSet = Sets.newHashSet();
                videoSet.add(video);
                videoLimitMap.put(specialLimitEnum, videoSet);
            });

            video.getTarget().forEach(targetEnum -> {
                if (videoTargetMap.containsKey(targetEnum)) {
                    videoTargetMap.get(targetEnum).add(video);
                    return;
                }

                Set videoSet = Sets.newHashSet();
                videoSet.add(video);
                videoTargetMap.put(targetEnum, videoSet);
            });
        }

        return workoutGenerateBO;
    }

    private Map<String, Map<Integer, ProjFitnessExerciseVideo>> listVideoI18nData(List<String> languages, Map<Integer, ProjFitnessExerciseVideo> videoIdMap) {

        Map<String, Map<Integer, ProjFitnessExerciseVideo>> videoI18nMap = Maps.newHashMap();
        videoI18nMap.put(GlobalConstant.DEFAULT_LANGUAGE, videoIdMap);
        List<ProjFitnessExerciseVideoI18n> i18nList = videoIdMap.values().stream().map(ProjFitnessExerciseVideoI18n::new).collect(Collectors.toList());


        Set<String> translationLanguages = languages.stream().filter(StringUtils::isNotBlank).filter(o -> !Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, o)).collect(Collectors.toSet());
        if (translationLanguages.isEmpty()) {
            return videoI18nMap;
        }

        Map<String, Map<Integer, ProjFitnessExerciseVideo>> multiVideoI18nMap = Maps.newHashMap();
        Map<Object, ProjFitnessExerciseVideoI18n> i18nModelGroupByKey = speechI18nPubService.getI18nModelGroupByKey(i18nList, translationLanguages, ProjCodeEnums.OOG104);
        i18nModelGroupByKey.forEach((key, i18nModel) -> {
            Map<LanguageEnums, AudioTranslateResultModel> guidanceResultMap = i18nModel.getGuidanceResult().stream().collect(
                    Collectors.toMap(AudioTranslateResultModel::getLanguage, Function.identity(),
                            (m, n) -> m));
            List<AudioTranslateResultModel> nameResults = i18nModel.getResult();
            nameResults.forEach(nameResult -> {
                LanguageEnums language = nameResult.getLanguage();
                Map<Integer, ProjFitnessExerciseVideo> langMap = multiVideoI18nMap.computeIfAbsent(language.getName(), k -> Maps.newHashMap());
                ProjFitnessExerciseVideo video = new ProjFitnessExerciseVideo();
                video.setName(nameResult.getText());
                video.setNameAudioUrl(nameResult.getAudioUrl());
                video.setNameAudioDuration(nameResult.getDuration());
                if (guidanceResultMap.containsKey(language)) {
                    AudioTranslateResultModel guidanceResult = guidanceResultMap.get(language);
                    video.setGuidance(guidanceResult.getText());
                    video.setGuidanceAudioUrl(guidanceResult.getAudioUrl());
                    video.setGuidanceAudioDuration(guidanceResult.getDuration());
                }
                langMap.put(Integer.valueOf(key.toString()), video);
            });
        });

        translationLanguages.forEach(language -> {
            if (!multiVideoI18nMap.containsKey(language)) {
                throw new BizException(String.format("The Fitness Exercise Video data translation incomplete, language is %s.", language));
            }
        });

        videoIdMap.keySet().forEach(videoId -> {
            translationLanguages.forEach(language -> {
                if (!multiVideoI18nMap.get(language).containsKey(videoId)) {
                    throw new BizException(String.format("The Fitness Exercise Video data translation incomplete, language is %s, dataId is %s.", language, videoId));
                }
            });
        });

        videoI18nMap.putAll(multiVideoI18nMap);
        return videoI18nMap;
    }

    private List<FitnessTemplateTaskBO> checkTemplateTask4Generate(List<ProjFitnessTemplateTask> templateTaskList, List<String> languageList) {

        Set<Integer> templateIds = templateTaskList.stream().map(ProjFitnessTemplateTask::getProjFitnessTemplateId).collect(Collectors.toSet());
        Collection<ProjFitnessTemplate> templateList = templateService.listByIds(templateIds);
        Map<Integer, ProjFitnessTemplate> templateIdMap = templateList.stream().collect(Collectors.toMap(ProjFitnessTemplate::getId, Function.identity()));

        List<ProjFitnessTemplateExerciseGroup> exerciseGroupList = exerciseGroupService.listByTemplateIds(templateIds);
        Map<Integer, List<ProjFitnessTemplateExerciseGroup>> templateAndGroupMap = exerciseGroupList.stream().collect(Collectors.groupingBy(ProjFitnessTemplateExerciseGroup::getProjFitnessTemplateId));

        List<FitnessTemplateTaskBO> templateTaskBOList = templateTaskList.stream().map(task -> {
            ProjFitnessTemplate template = templateIdMap.get(task.getProjFitnessTemplateId());
            List<ProjFitnessTemplateExerciseGroup> exerciseGroup = templateAndGroupMap.get(task.getProjFitnessTemplateId());

            Map<ManualTypeEnums, ProjFitnessTemplateExerciseGroup> typeAndGroupMap = exerciseGroup.stream().collect(Collectors.toMap(ProjFitnessTemplateExerciseGroup::getGroupType, Function.identity(), (k1, k2) -> k1));
            FitnessTemplateTaskBO templateTaskBO = new FitnessTemplateTaskBO(task, template, typeAndGroupMap, languageList);
            return templateTaskBO;
        }).collect(Collectors.toList());

        return templateTaskBOList;
    }

    @Override
    public PageRes<ProjFitnessWorkoutGeneratePageVO> page(ProjFitnessWorkoutGeneratePageReq req) {
        IPage<Integer> page = this.pageWorkoutIds(req, new Page<>(req.getPageNum(), req.getPageSize()));
        // 为空直接返回
        if (CollUtil.isEmpty(page.getRecords())) {
            return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), Collections.emptyList());
        }
        //查询数据
        LambdaQueryWrapper<ProjFitnessWorkoutGenerate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjFitnessWorkoutGenerate::getId, page.getRecords()).orderByDesc(ProjFitnessWorkoutGenerate::getId);
        List<ProjFitnessWorkoutGenerate> workoutList = baseMapper.selectList(queryWrapper);

        //补充查询任务信息
        //1.获取taskIdSet
        Set<Integer> taskIds = workoutList.stream().map(ProjFitnessWorkoutGenerate::getProjFitnessTemplateTaskId).collect(Collectors.toSet());
        //2.查询任务信息
        Map<Integer, ProjFitnessTemplateTask> taskMap = taskService.listByIds(taskIds).stream().collect(Collectors.toMap(ProjFitnessTemplateTask::getId, v -> v));
        //3.补充任务信息
        List<ProjFitnessWorkoutGeneratePageVO> vos = workoutList.stream().map(w -> {
            ProjFitnessWorkoutGeneratePageVO vo = mapStruct.toPageVO(w);
            ProjFitnessTemplateTask task = taskMap.get(vo.getProjFitnessTemplateTaskId());
            if (task != null) {
                vo.setGenerateNum(task.getWorkoutNum());
                vo.setCreateUser(task.getCreateUser());
                vo.setCleanUp(task.getCleanUp());
                vo.setUpdateStatus(w.getFileStatus());
            }
            return vo;
        }).collect(Collectors.toList());
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), vos);
    }

    private IPage<Integer> pageWorkoutIds(ProjFitnessWorkoutGeneratePageReq req, Page<ProjFitnessWorkoutGenerate> page) {
        PageReqResult result = getPageReqResult(req);
        return baseMapper.page(page, req, result.templateIdSet, result.minDuration, result.maxDuration);
    }

    private List<Integer> listWorkoutIds(ProjFitnessWorkoutGeneratePageReq req) {
        PageReqResult result = getPageReqResult(req);
        return baseMapper.list(req, result.templateIdSet, result.minDuration, result.maxDuration);
    }

    private PageReqResult getPageReqResult(ProjFitnessWorkoutGeneratePageReq req) {
        Integer templateStatus = req.getTemplateStatus();
        Integer projFitnessTemplateId = req.getProjFitnessTemplateId();
        ManualDifficultyEnums templateLevel = req.getTemplateLevel();
        Set<Integer> templateIdSet = null;

        if (projFitnessTemplateId == null && (templateStatus != null || templateLevel != null)) {
            LambdaQueryWrapper<ProjFitnessTemplate> templateWrapper = new LambdaQueryWrapper<>();
            templateWrapper.eq(templateStatus != null, ProjFitnessTemplate::getStatus, templateStatus)
                    .eq(templateLevel != null, ProjFitnessTemplate::getLevel, templateLevel)
                    .eq(ProjFitnessTemplate::getDelFlag, 0);
            List<ProjFitnessTemplate> templates = templateService.list(templateWrapper);
            templateIdSet = templates.stream().map(BaseModel::getId).collect(Collectors.toSet());
        }

        WorkoutDurationRangeEnums time = req.getTime();
        Integer minDuration = time != null ? time.getMin() : null;
        Integer maxDuration = time != null ? time.getMax() : null;
        return new PageReqResult(templateIdSet, minDuration, maxDuration);
    }

    private static class PageReqResult {
        public final Set<Integer> templateIdSet;
        public final Integer minDuration;
        public final Integer maxDuration;

        public PageReqResult(Set<Integer> templateIdSet, Integer minDuration, Integer maxDuration) {
            this.templateIdSet = templateIdSet;
            this.minDuration = minDuration;
            this.maxDuration = maxDuration;
        }
    }

    @Override
    public ProjFitnessWorkoutGenerateDetailVO findDetailById(Integer id) {
        ProjFitnessWorkoutGenerate workout = baseMapper.selectById(id);
        BizExceptionUtil.throwIf(workout == null, "Workout not found");
        ProjFitnessWorkoutGenerateDetailVO vo = mapStruct.toDetailVO(workout);
        List<ProjFitnessWorkoutGenerateVideoVO> videoVOs = new ArrayList<>();
        //查询关联的视频
        List<ProjFitnessWorkoutGenerateExerciseVideo> relations = generateVideoService.listByWorkoutGenerateId(id);
        if (CollUtil.isNotEmpty(relations)) {
            Map<Integer, Integer> relationMap = relations.stream()
                    .collect(Collectors.toMap(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessExerciseVideoId,
                            ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessTemplateExerciseGroupId));
            List<Integer> relationVideoIds = relations.stream().map(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessExerciseVideoId).collect(Collectors.toList());
            Set<Integer> videoIds = relationMap.keySet();
            List<ProjFitnessExerciseVideoPageVO> videos = videoService.listVOByIds(new ArrayList<>(videoIds));

            Set<Integer> groupIds = new HashSet<>(relationMap.values());
            Collection<ProjFitnessTemplateExerciseGroup> groups = exerciseGroupService.listByIds(groupIds);
            Map<Integer, Integer> groupIdMap = groups.stream()
                    .collect(Collectors.toMap(ProjFitnessTemplateExerciseGroup::getId,
                            ProjFitnessTemplateExerciseGroup::getRounds,
                            (v1, v2) -> v1));

            videoVOs = videos.stream().map(v -> {
                        ProjFitnessWorkoutGenerateVideoVO videoVO = videoMapStruct.toGenerateVideoVO(v);
                        Integer groupId = relationMap.get(v.getId());
                        Integer rounds = groupId != null ? groupIdMap.getOrDefault(groupId, 1) : 1;
                        videoVO.setExerciseCircuit(rounds);
                        return videoVO;
                    }).sorted(Comparator.comparingInt(v -> relationVideoIds.indexOf(v.getId())))
                    .collect(Collectors.toList());
        }
        vo.setVideoList(videoVOs);
        //查询temp详情
        vo.setTemplateDetail(templateService.detail(workout.getProjFitnessTemplateId()));
        return vo;
    }

    @Transactional
    @Override
    public void update(ProjFitnessWorkoutGenerateUpdateReq workoutUpdateReq, Integer projId) {
        Integer id = workoutUpdateReq.getId();
        ProjFitnessWorkoutGenerate workoutGenerate = baseMapper.selectById(id);
        if (null == workoutGenerate) {
            throw new BizException("workout not found");
        }
        List<Integer> videoIdList = workoutUpdateReq.getVideoIdList();
        List<ProjFitnessExerciseVideo> videos = videoService.listByIds(videoIdList)
                .stream().sorted(Comparator.comparingInt(v -> videoIdList.indexOf(v.getId())))
                .collect(Collectors.toList());
        checkLeftRight(videos);
        RelationAndGenerateBOs result = assembleRelationAndGenerateBOs(workoutGenerate, videos);
        //处理视频音频信息
        Set<String> languages = CollUtil.newLinkedHashSet(GlobalConstant.DEFAULT_LANGUAGE);
        ProjInfo projInfo = projInfoService.getById(projId);
        languages.addAll(StrUtil.split(projInfo.getLanguages(), GlobalConstant.COMMA, true, true));
        BaseWorkoutBO baseWorkoutBO = this.generateBaseWorkout(result.generateVideoBOS, new ArrayList<>(languages), true, null, null);
        //更新workoutGenerate
        workoutGenerate.setAudioJsonUrl(baseWorkoutBO.getAudioJsonUrl());
        workoutGenerate.setDuration(baseWorkoutBO.getDuration());
        workoutGenerate.setCalorie(baseWorkoutBO.getCalorie());
        workoutGenerate.setVideoUrl(baseWorkoutBO.getVideo2532Url());
        workoutGenerate.setAudioLanguages(CollUtil.join(languages, GlobalConstant.COMMA));
        workoutGenerate.setFileStatus(GlobalConstant.ZERO);//返回了结果默认就是0-成功
        super.updateById(workoutGenerate);
        //更新国际化
        Map<String, String> i18nMap = MapUtil.defaultIfEmpty(baseWorkoutBO.getAudioI18nUrl(), new HashMap<>());
        List<ProjFitnessWorkoutGenerateI18n> i18nWorkoutList = i18nMap.entrySet().stream().map(entry -> {
            ProjFitnessWorkoutGenerateI18n workoutI18n = new ProjFitnessWorkoutGenerateI18n();
            workoutI18n.setProjFitnessWorkoutGenerateId(workoutGenerate.getId());
            workoutI18n.setLanguage(entry.getKey());
            workoutI18n.setAudioJsonUrl(entry.getValue());
            workoutI18n.setProjId(projId);
            return workoutI18n;
        }).collect(Collectors.toList());
        //delete old i18nData
        LambdaUpdateWrapper<ProjFitnessWorkoutGenerateI18n> deleteWrapper = new LambdaUpdateWrapper<>();
        deleteWrapper.eq(ProjFitnessWorkoutGenerateI18n::getProjFitnessWorkoutGenerateId, workoutGenerate.getId());
        deleteWrapper.eq(ProjFitnessWorkoutGenerateI18n::getProjId, projId);
        deleteWrapper.eq(ProjFitnessWorkoutGenerateI18n::getDelFlag, 0);
        workoutGenerateI18nService.remove(deleteWrapper);
        if (CollUtil.isNotEmpty(i18nWorkoutList)) workoutGenerateI18nService.saveBatch(i18nWorkoutList);
        //填充workoutGenerateExerciseVideo
        Map<Integer, BaseGenerateVideoBO> videoIdMap = result.generateVideoBOS.stream().collect(
                Collectors.toMap(b -> b.getVideo().getId(),
                        Function.identity()));
        List<ProjFitnessWorkoutGenerateExerciseVideo> relations = result.videoRelationList.stream()
                .peek(r -> {
                    BaseGenerateVideoBO bo = videoIdMap.get(r.getProjFitnessExerciseVideoId());
                    r.setPreviewDuration(bo.getPreviewDuration());
                    r.setVideoDuration(bo.getVideoDuration());
                })
                .collect(Collectors.toList());
        //删除关联的视频
        generateVideoService.deleteByWorkoutIds(Collections.singleton(id));
        if (CollUtil.isEmpty(relations)) {
            return;
        }
        generateVideoService.saveBatch(relations);
    }

    private RelationAndGenerateBOs assembleRelationAndGenerateBOs(ProjFitnessWorkoutGenerate workoutGenerate,
                                                                  Collection<ProjFitnessExerciseVideo> videos) {
        Map<ManualTypeEnums, ProjFitnessTemplateExerciseGroup> groupIdMap = this.getGroupMap(workoutGenerate.getProjFitnessTemplateId());
        List<ProjFitnessWorkoutGenerateExerciseVideo> videoRelationList = new ArrayList<>(videos.size());
        List<BaseGenerateVideoBO> generateVideoBOS = new ArrayList<>(videos.size());
        videos.forEach(item -> {
            ProjFitnessTemplateExerciseGroup group = groupIdMap.get(item.getType());
            ProjFitnessWorkoutGenerateExerciseVideo videoRelation = new ProjFitnessWorkoutGenerateExerciseVideo();
            videoRelation.setProjFitnessExerciseVideoId(item.getId())
                    .setProjFitnessWorkoutGenerateId(workoutGenerate.getId())
                    .setProjFitnessTemplateId(workoutGenerate.getProjFitnessTemplateId())
                    .setProjFitnessTemplateExerciseGroupId(group.getId());
            videoRelationList.add(videoRelation);
            BaseGenerateVideoBO generateVideoBO = new BaseGenerateVideoBO();
            generateVideoBO.setVideoRound(group.getRounds());
            generateVideoBO.setGroupId(group.getId());
            generateVideoBO.setVideo(item);
            generateVideoBOS.add(generateVideoBO);
        });
        return new RelationAndGenerateBOs(videoRelationList, generateVideoBOS);
    }

    @Data
    private static class RelationAndGenerateBOs {
        public final List<ProjFitnessWorkoutGenerateExerciseVideo> videoRelationList;
        public final List<BaseGenerateVideoBO> generateVideoBOS;
    }

    private Map<ManualTypeEnums, ProjFitnessTemplateExerciseGroup> getGroupMap(Integer id) {
        LambdaQueryWrapper<ProjFitnessTemplateExerciseGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessTemplateExerciseGroup::getDelFlag, 0)
                .eq(ProjFitnessTemplateExerciseGroup::getProjFitnessTemplateId, id);
        List<ProjFitnessTemplateExerciseGroup> groups = groupMapper.selectList(wrapper);
        return groups.stream().collect(
                Collectors.toMap(ProjFitnessTemplateExerciseGroup::getGroupType, v -> v));
    }

    private void checkLeftRight(Collection<ProjFitnessExerciseVideo> videoList) {
        // key：right video id,value:相同id的个数
        ProjFitnessExerciseVideo preLeftVideo = null;
        for (ProjFitnessExerciseVideo video : videoList) {
            if (null != preLeftVideo && !Objects.equals(preLeftVideo.getLeftRightVideoId(), video.getId())) {
                throw new BizException(String.format("Left and right must be adjacent, left video name: %s", preLeftVideo.getName()));
            }
            if (ManualVideoDirectionEnums.LEFT.equals(video.getVideoDirection())) {
                preLeftVideo = video;
            } else {
                preLeftVideo = null;
            }
        }
        //增加最后一个视频的校验
        if (null != preLeftVideo) {
            throw new BizException(String.format("Left and right must be adjacent, left video name: %s", preLeftVideo.getName()));
        }
    }

    @Override
    public PageRes<ProjFitnessExerciseVideoPageVO> pageVideo(PageReq pageReq, Integer id) {
        LambdaQueryWrapper<ProjFitnessWorkoutGenerateExerciseVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessWorkoutGenerateId, id)
                .eq(ProjFitnessWorkoutGenerateExerciseVideo::getDelFlag, 0)
                .orderByAsc(ProjFitnessWorkoutGenerateExerciseVideo::getId);
        IPage<ProjFitnessWorkoutGenerateExerciseVideo> page = generateVideoService.page(new Page<>(pageReq.getPageNum(), pageReq.getPageSize()), queryWrapper);
        List<ProjFitnessWorkoutGenerateExerciseVideo> relations = page.getRecords();
        if (CollUtil.isEmpty(relations)) {
            return new PageRes<>(pageReq.getPageNum(), pageReq.getPageSize(), 0L, 0L, Collections.emptyList());
        }
        //获取videoIdSet
        List<Integer> videoIds = relations.stream().map(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessExerciseVideoId).collect(Collectors.toList());
        //查询video信息
        List<ProjFitnessExerciseVideoPageVO> videos = videoService.listVOByIds(videoIds);
        // 按 videoIds 顺序排序
        Map<Integer, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < videoIds.size(); i++) {
            orderMap.put(videoIds.get(i), i);
        }
        videos.sort(Comparator.comparingInt(v -> orderMap.getOrDefault(v.getId(), Integer.MAX_VALUE)));
        return new PageRes<>(pageReq.getPageNum(), pageReq.getPageSize(), page.getTotal(), page.getPages(), videos);
    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjFitnessWorkoutGenerate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessWorkoutGenerate::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.set(ProjFitnessWorkoutGenerate::getUpdateTime, LocalDateTime.now());
        wrapper.set(ProjFitnessWorkoutGenerate::getUpdateUser, RequestContextUtils.getLoginUserName());
        wrapper.in(ProjFitnessWorkoutGenerate::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjFitnessWorkoutGenerate::getId, idList);
        update(new ProjFitnessWorkoutGenerate(), wrapper);
    }


    @Override
    public void updateDisableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        Set<Integer> idSet = new HashSet<>(idList);
        //TODO 补充校验逻辑
        List<Object> workoutRelations = new ArrayList<>();
        BizExceptionUtil.throwIf(CollUtil.isNotEmpty(workoutRelations), "This workout cannot be disabled because it is used in the following images");
        LambdaUpdateWrapper<ProjFitnessWorkoutGenerate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessWorkoutGenerate::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.set(ProjFitnessWorkoutGenerate::getUpdateTime, LocalDateTime.now());
        wrapper.set(ProjFitnessWorkoutGenerate::getUpdateUser, RequestContextUtils.getLoginUserName());
        wrapper.eq(ProjFitnessWorkoutGenerate::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFitnessWorkoutGenerate::getId, idList);
        this.update(new ProjFitnessWorkoutGenerate(), wrapper);
    }

    @Override
    public void deleteByIdList(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        //TODO 补充校验逻辑
        List<Object> workoutRelations = new ArrayList<>();
        BizExceptionUtil.throwIf(CollUtil.isNotEmpty(workoutRelations), "This workout cannot be deleted because it is used in the following images");
        LambdaUpdateWrapper<ProjFitnessWorkoutGenerate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessWorkoutGenerate::getDelFlag, GlobalConstant.YES)
                .set(ProjFitnessWorkoutGenerate::getUpdateTime, LocalDateTime.now())
                .set(ProjFitnessWorkoutGenerate::getUpdateUser, RequestContextUtils.getLoginUserName())
                .in(ProjFitnessWorkoutGenerate::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_NOT_READY)
                .in(ProjFitnessWorkoutGenerate::getId, idList);
        this.update(new ProjFitnessWorkoutGenerate(), wrapper);
    }

    @Override
    public ProjFitnessWorkoutGenerate getByTemplateIdAndTaskId(Integer templateId, Integer taskId) {
        LambdaQueryWrapper<ProjFitnessWorkoutGenerate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessWorkoutGenerate::getProjFitnessTemplateId, templateId)
                .eq(ProjFitnessWorkoutGenerate::getProjFitnessTemplateTaskId, taskId)
                .eq(ProjFitnessWorkoutGenerate::getDelFlag, 0);
        return getOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveWorkoutGenerate(ProjFitnessWorkoutGenerate workoutGenerate) {
        if (workoutGenerate == null) {
            throw new RuntimeException("参数不能为空");
        }
        save(workoutGenerate);
        return workoutGenerate.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWorkoutGenerate(ProjFitnessWorkoutGenerate workoutGenerate) {
        if (workoutGenerate == null || workoutGenerate.getId() == null) {
            throw new RuntimeException("参数不能为空");
        }
        updateById(workoutGenerate);
    }

    @Override
    public void updateFileStatus(Integer id, Integer fileStatus, String failMessage) {
        if (id == null || fileStatus == null) {
            throw new RuntimeException("参数不能为空");
        }
        ProjFitnessWorkoutGenerate workoutGenerate = new ProjFitnessWorkoutGenerate();
        workoutGenerate.setId(id);
        workoutGenerate.setFileStatus(fileStatus);
        workoutGenerate.setFailMessage(failMessage);
        updateById(workoutGenerate);
    }


    @Override
    public void generateM3u8Interrupt() {
        generateM3u8Queue.clear();
        //update all fileStatus to 0
        LambdaUpdateWrapper<ProjFitnessWorkoutGenerate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjFitnessWorkoutGenerate::getFileStatus, GlobalConstant.ONE)
                .set(ProjFitnessWorkoutGenerate::getFileStatus, GlobalConstant.ZERO);
        baseMapper.update(new ProjFitnessWorkoutGenerate(), updateWrapper);
    }

    @Override
    public Integer generateM3u8ByQuery(ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req) {
        //query all worout ids
        List<Integer> workoutIds = getAllWorkoutIds(m3u8Req);
        if (CollUtil.isEmpty(workoutIds)) return 0;
        LoginUserInfo userInfo = RequestContextUtils.getLoginUser();
        String userName = Objects.isNull(userInfo) ? GlobalConstant.UNKNOWN_USER : userInfo.getUserName();
        //assemble req List
        List<ProjFitnessManualWorkoutGenerateM3u8Req> reqList = assembleReqList(m3u8Req, workoutIds, userName);
        //add to queue
        generateM3u8Queue.addAll(reqList);
        //update workout's fileStatus to 1-processing
        LambdaUpdateWrapper<ProjFitnessWorkoutGenerate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProjFitnessWorkoutGenerate::getId, workoutIds);
        this.updateWorkoutFileStatus(GlobalConstant.ONE, userName, updateWrapper);
        //async running generateM3u8
        ProjInfo projInfo = projInfoService.getById(m3u8Req.getProjId());
        CompletableFuture.runAsync(() -> {
            runQueueGenerateM3u8(projInfo, userName);
        });
        return workoutIds.size();
    }

    private void runQueueGenerateM3u8(ProjInfo projInfo, String userName) {
        if (!generateM3u8ReqIsRunning.compareAndSet(false, true)) {
            log.warn("fitnessWorkoutGenerate QueueGenerateM3u8: is running, already add req to queue");
            return;
        }
        log.info("start running fitnessWorkoutGenerate QueueGenerateM3u8:");
        ProjFitnessManualWorkoutGenerateM3u8Req[] reqArray = new ProjFitnessManualWorkoutGenerateM3u8Req[1];
        try {
            ProjFitnessWorkoutGenerateServiceImpl bean = SpringUtil.getBean(ProjFitnessWorkoutGenerateServiceImpl.class);
            while ((reqArray[0] = generateM3u8Queue.poll()) != null) {
                final ProjFitnessManualWorkoutGenerateM3u8Req req = reqArray[0];
                log.info("fitnessWorkoutGenerate QueueGenerateM3u8 left pool size:{} , req:{}", generateM3u8Queue.size(), req);
                M3u8GenerateContext context = loadM3u8GenerateContext(req);
                List<ProjFitnessWorkoutGenerate> failedWorkouts = new ArrayList<>();
                List<CompletableFuture<List<ProjFitnessWorkoutGenerateI18n>>> completableFutures = context.getWorkoutsDetails().stream().map(
                        workout -> CompletableFuture.supplyAsync(() -> {
                            try {
                                List<ProjFitnessWorkoutGenerateExerciseVideo> relationList = context.getRelationMap().get(workout.getId());
                                if (CollUtil.isEmpty(relationList))
                                    return new ArrayList<ProjFitnessWorkoutGenerateI18n>();
                                List<BaseGenerateVideoBO> bos = assembleGenerateBOs(workout, context.getVideoMap(), context.getGroupMap(), relationList);
                                FitnessWorkoutGenerateSoundBO soundBO = context.getExerciseSoundMap().get(bos.get(GlobalConstant.ZERO).getVideo().getExerciseType());
                                Set<String> languages = getLanguages(req.getLanguages(), projInfo);
                                BaseWorkoutBO baseWorkoutBO = bean.generateBaseWorkout(bos, new ArrayList<>(languages), req.isVideoFlag(), context.getVideoBO(), soundBO);
                                return updateGenerateResult(req, workout, baseWorkoutBO, languages, projInfo, userName, bos, relationList);
                            } catch (Exception e) {
                                log.error("Failed to generate M3U8 for workout: {}", workout.getId(), e);
                                failedWorkouts.add(workout);
                                return new ArrayList<ProjFitnessWorkoutGenerateI18n>();
                            }
                        }, generateM3u8Executor)).collect(Collectors.toList());
                // 等待所有任务完成，并合并所有结果
                List<ProjFitnessWorkoutGenerateI18n> allI18nWorkoutList = completableFutures.stream()
                        .map(CompletableFuture::join).flatMap(List::stream).collect(Collectors.toList());
                // 剔除失败的 workout 和关系表
                try {
                    transactionTemplate.executeWithoutResult(status ->
                            processGenerateM3u8Update(req, projInfo, allI18nWorkoutList, context.getRelationVideos(), bean, context.getWorkoutsDetails(), failedWorkouts, userName));
                } catch (Exception e) {
                    log.error("batch update fitness generate workout error", e);
                    updateWorkoutFileStatus(GlobalConstant.TWO, userName, context.getWorkoutsDetails().stream().map(ProjFitnessWorkoutGenerate::getId).collect(Collectors.toList()));
                }
            }
            log.info("end running fitnessWorkoutGenerate QueueGenerateM3u8");
        } catch (Exception e) {
            log.error("Failed to generate M3U8", e);
        } finally {
            generateM3u8ReqIsRunning.set(false);
        }
    }

    private Set<String> getLanguages(List<String> req, ProjInfo projInfo) {
        Set<String> languages = new HashSet<>();
        if (CollUtil.isEmpty(req)) {
            languages.add(GlobalConstant.DEFAULT_LANGUAGE);
            languages.addAll(StrUtil.split(projInfo.getLanguages(), GlobalConstant.COMMA, true, true));
        } else {
            languages.addAll(req);
        }
        return languages;
    }


    private M3u8GenerateContext loadM3u8GenerateContext(ProjFitnessManualWorkoutGenerateM3u8Req req) {
        M3u8GenerateContext context = new M3u8GenerateContext();
        List<ProjFitnessWorkoutGenerate> workouts = this.list(new LambdaQueryWrapper<ProjFitnessWorkoutGenerate>()
                .in(ProjFitnessWorkoutGenerate::getId, req.getWorkoutIds()));
        context.setWorkoutsDetails(workouts);
        if (CollUtil.isEmpty(workouts)) {
            return context;
        }
        LambdaQueryWrapper<ProjFitnessWorkoutGenerateExerciseVideo> relationWrapper = new LambdaQueryWrapper<ProjFitnessWorkoutGenerateExerciseVideo>()
                .in(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessWorkoutGenerateId, req.getWorkoutIds());
        relationWrapper.orderByAsc(ProjFitnessWorkoutGenerateExerciseVideo::getId);
        List<ProjFitnessWorkoutGenerateExerciseVideo> relations = generateVideoService.list(relationWrapper);
        context.setRelationVideos(relations);
        Map<Integer, List<ProjFitnessWorkoutGenerateExerciseVideo>> relMap = relations.stream()
                .collect(Collectors.groupingBy(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessWorkoutGenerateId));
        context.setRelationMap(relMap);

        Set<Integer> videoIds = relations.stream()
                .map(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessExerciseVideoId)
                .collect(Collectors.toSet());
        Map<Integer, ProjFitnessExerciseVideo> vMap = videoService.list(new LambdaQueryWrapper<ProjFitnessExerciseVideo>()
                        .in(ProjFitnessExerciseVideo::getId, videoIds))
                .stream().collect(Collectors.toMap(ProjFitnessExerciseVideo::getId, v -> v));
        context.setVideoMap(vMap);

        Set<Integer> groupIds = relations.stream()
                .map(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessTemplateExerciseGroupId)
                .collect(Collectors.toSet());
        Map<Integer, ProjFitnessTemplateExerciseGroup> gMap = exerciseGroupService.list(new LambdaQueryWrapper<ProjFitnessTemplateExerciseGroup>()
                        .in(ProjFitnessTemplateExerciseGroup::getId, groupIds))
                .stream().collect(Collectors.toMap(ProjFitnessTemplateExerciseGroup::getId, g -> g));
        context.setGroupMap(gMap);

        Set<ManualExerciseTypeEnums> exerciseTypeSet = vMap.values().stream()
                .map(ProjFitnessExerciseVideo::getExerciseType)
                .collect(Collectors.toSet());
        Map<ManualExerciseTypeEnums, FitnessWorkoutGenerateSoundBO> soundMap = createGenerateSoundBO(req.getLanguages(), exerciseTypeSet);
        context.setExerciseSoundMap(soundMap);

        Map<String, Map<Integer, ProjFitnessExerciseVideo>> videoI18nMap = listVideoI18nData(req.getLanguages(), vMap);
        FitnessWorkoutGenerateVideoBO videoBO = new FitnessWorkoutGenerateVideoBO();
        videoBO.setVideoI18nMap(videoI18nMap);
        context.setVideoBO(videoBO);
        return context;
    }

    private void processGenerateM3u8Update(ProjFitnessManualWorkoutGenerateM3u8Req req, ProjInfo projInfo, List<ProjFitnessWorkoutGenerateI18n> allI18nWorkoutList,
                                           List<ProjFitnessWorkoutGenerateExerciseVideo> relationVideos, ProjFitnessWorkoutGenerateServiceImpl bean,
                                           List<ProjFitnessWorkoutGenerate> workoutsDetails, List<ProjFitnessWorkoutGenerate> failedWorkouts, String userName) {

        log.info("batch update database fitness generate workout:{}", req.getWorkoutIds());
        //选择了音频更新才需更新audioJSON数据
        if (req.isAudioFlag()) {
            //delete old i18nData
            LambdaUpdateWrapper<ProjFitnessWorkoutGenerateI18n> deleteWrapper = new LambdaUpdateWrapper<>();
            deleteWrapper.in(ProjFitnessWorkoutGenerateI18n::getProjFitnessWorkoutGenerateId, req.getWorkoutIds());
            deleteWrapper.eq(ProjFitnessWorkoutGenerateI18n::getProjId, projInfo.getId());
            deleteWrapper.eq(ProjFitnessWorkoutGenerateI18n::getDelFlag, 0);
            deleteWrapper.in(CollUtil.isNotEmpty(req.getLanguages()), ProjFitnessWorkoutGenerateI18n::getLanguage, req.getLanguages());
            workoutGenerateI18nService.remove(deleteWrapper);
            //save new i18nData
            if (CollUtil.isNotEmpty(allI18nWorkoutList)) workoutGenerateI18nService.saveBatch(allI18nWorkoutList);
        }
        //选择了视频更新才需更新workoutGenerateExerciseVideo数据
        if (req.isVideoFlag()) {
            //update workoutGenerateExerciseVideo
            generateVideoService.updateBatchById(relationVideos);
        }
        //update workoutGenerate
        workoutsDetails.removeAll(failedWorkouts);
        bean.updateBatchById(workoutsDetails);
        //update failed workout's fileStatus to 2-failed
        if (CollUtil.isNotEmpty(failedWorkouts)) {
            updateWorkoutFileStatus(GlobalConstant.TWO, userName, failedWorkouts.stream().map(ProjFitnessWorkoutGenerate::getId).collect(Collectors.toList()));
        }
    }

    private List<ProjFitnessWorkoutGenerateI18n> updateGenerateResult(ProjFitnessManualWorkoutGenerateM3u8Req req, ProjFitnessWorkoutGenerate workout,
                                                                      BaseWorkoutBO baseWorkoutBO, Set<String> languages,
                                                                      ProjInfo projInfo, String userName,
                                                                      List<BaseGenerateVideoBO> bos, List<ProjFitnessWorkoutGenerateExerciseVideo> relationList) {
        //更新workoutGenerate
        //选择了m3u8更新才需更新duration、calorie、videoUrl字段
        if (req.isVideoFlag()) {
            workout.setDuration(baseWorkoutBO.getDuration());
            workout.setCalorie(baseWorkoutBO.getCalorie());
            workout.setVideoUrl(baseWorkoutBO.getVideo2532Url());
        }
        //选择了音频更新才需更新audioJsonUrl、audioLanguages字段
        if (req.isAudioFlag()) {
            workout.setAudioJsonUrl(baseWorkoutBO.getAudioJsonUrl());
            workout.setAudioLanguages(CollUtil.join(languages, GlobalConstant.COMMA));
        }
        workout.setFileStatus(GlobalConstant.ZERO);//返回了结果默认就是0-成功
        workout.setUpdateUser(req.getUpdateUser());
        //更新国际化
        Map<String, String> i18nMap = MapUtil.defaultIfEmpty(baseWorkoutBO.getAudioI18nUrl(), new HashMap<>());
        List<ProjFitnessWorkoutGenerateI18n> i18nWorkoutList = i18nMap.entrySet().stream().map(entry -> {
            ProjFitnessWorkoutGenerateI18n workoutI18n = new ProjFitnessWorkoutGenerateI18n();
            workoutI18n.setProjFitnessWorkoutGenerateId(workout.getId());
            workoutI18n.setLanguage(entry.getKey());
            workoutI18n.setAudioJsonUrl(entry.getValue());
            workoutI18n.setProjId(projInfo.getId());
            workoutI18n.setCreateUser(userName);
            return workoutI18n;
        }).collect(Collectors.toList());

        //填充workoutGenerateExerciseVideo
        if (req.isVideoFlag()) {
            Map<Integer, BaseGenerateVideoBO> baseGenerateVideoIdMap = bos.stream().collect(
                    Collectors.toMap(b -> b.getVideo().getId(), Function.identity()));
            relationList.forEach(r -> {
                BaseGenerateVideoBO bo = baseGenerateVideoIdMap.get(r.getProjFitnessExerciseVideoId());
                r.setPreviewDuration(bo.getPreviewDuration());
                r.setVideoDuration(bo.getVideoDuration());
                r.setUpdateUser(userName);
            });
        }
        return i18nWorkoutList;
    }

    private List<ProjFitnessManualWorkoutGenerateM3u8Req> assembleReqList(ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req, List<Integer> workoutIds, String userName) {
        List<List<Integer>> batches = ListUtil.split(workoutIds, GlobalConstant.FIVE_HUNDRED);
        return batches.stream().map(ids -> {
            ProjFitnessManualWorkoutGenerateM3u8Req req = new ProjFitnessManualWorkoutGenerateM3u8Req();
            BeanUtil.copyProperties(m3u8Req, req);
            req.setWorkoutIds(ids);
            req.setUpdateUser(userName);
            return req;
        }).collect(Collectors.toList());
    }

    private List<Integer> getAllWorkoutIds(ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req) {
        List<ProjFitnessWorkoutGenerate> workouts;
        if (CollUtil.isNotEmpty(m3u8Req.getWorkoutIds())) {
            workouts = this.list(new LambdaQueryWrapper<ProjFitnessWorkoutGenerate>()
                    .in(ProjFitnessWorkoutGenerate::getId, m3u8Req.getWorkoutIds())
                    .select(ProjFitnessWorkoutGenerate::getId));
            return workouts.stream().map(ProjFitnessWorkoutGenerate::getId).collect(Collectors.toList());
        } else {
            return this.listWorkoutIds(m3u8Req.getPageReq());
        }
    }

    private void updateWorkoutFileStatus(int fileStatus, String userName, List<Integer> workoutsDetails) {
        this.updateWorkoutFileStatus(fileStatus, userName, workoutsDetails, null);
    }

    private void updateWorkoutFileStatus(int fileStatus, String userName, LambdaUpdateWrapper<ProjFitnessWorkoutGenerate> updateWrapper) {
        this.updateWorkoutFileStatus(fileStatus, userName, null, updateWrapper);
    }

    private void updateWorkoutFileStatus(int fileStatus, String userName, List<Integer> workoutsDetails,
                                         LambdaUpdateWrapper<ProjFitnessWorkoutGenerate> updateWrapper) {
        if (updateWrapper == null) {
            updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(ProjFitnessWorkoutGenerate::getId, workoutsDetails);
        }
        //update workout's fileStatus
        updateWrapper.set(ProjFitnessWorkoutGenerate::getFileStatus, fileStatus)
                .set(ProjFitnessWorkoutGenerate::getUpdateTime, LocalDateTime.now())
                .set(ProjFitnessWorkoutGenerate::getUpdateUser, userName);
        this.update(new ProjFitnessWorkoutGenerate(), updateWrapper);
    }

    @Override
    public Boolean generateM3u8ByPageReq(ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req) {
        //query workouts
        List<ProjFitnessWorkoutGenerate> workouts;
        List<Integer> workoutIds;
        if (CollUtil.isNotEmpty(m3u8Req.getWorkoutIds())) {
            workouts = this.list(new LambdaQueryWrapper<ProjFitnessWorkoutGenerate>()
                    .in(ProjFitnessWorkoutGenerate::getId, m3u8Req.getWorkoutIds())
                    .select(ProjFitnessWorkoutGenerate::getId));
            workoutIds = workouts.stream().map(ProjFitnessWorkoutGenerate::getId).collect(Collectors.toList());
        } else {
            workoutIds = this.listWorkoutIds(m3u8Req.getPageReq());
        }
        if (CollUtil.isEmpty(workoutIds)) {
            return null;
        }
        LoginUserInfo userInfo = RequestContextUtils.getLoginUser();
        String userName = Objects.isNull(userInfo) ? GlobalConstant.UNKNOWN_USER : userInfo.getUserName();
        List<Integer> finalWorkoutIds = workoutIds;
        CompletableFuture.runAsync(() -> {
            List<List<Integer>> batches = ListUtil.split(finalWorkoutIds, 500);
            for (List<Integer> batch : batches) {
                ProjFitnessManualWorkoutGenerateM3u8Req req = new ProjFitnessManualWorkoutGenerateM3u8Req();
                BeanUtil.copyProperties(m3u8Req, req);
                req.setWorkoutIds(batch);
                req.setUpdateUser(userName);
                this.generateM3u8(req);
            }
        });
        return true;
    }

    @Override
    public Boolean generateM3u8(ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req) {
        //query workouts
        List<ProjFitnessWorkoutGenerate> workouts = this.list(new LambdaQueryWrapper<ProjFitnessWorkoutGenerate>()
                .in(ProjFitnessWorkoutGenerate::getId, m3u8Req.getWorkoutIds()));
        if (CollUtil.isEmpty(workouts)) {
            return null;
        }
        //update workout's fileStatus to 1-processing
        updateWorkoutFileStatus(GlobalConstant.ONE, RequestContextUtils.getLoginUserName(), m3u8Req.getWorkoutIds());
        LoginUserInfo userInfo = RequestContextUtils.getLoginUser();
        String userName = Objects.isNull(userInfo) ? StrUtil.blankToDefault(m3u8Req.getUpdateUser(), GlobalConstant.UNKNOWN_USER) : userInfo.getUserName();
        ProjInfo projInfo = projInfoService.getById(m3u8Req.getProjId());
        asyncGenerateM3U8(workouts, userName, projInfo, m3u8Req.getLanguages());
        return true;
    }

    private void asyncGenerateM3U8(List<ProjFitnessWorkoutGenerate> workouts, String userName, ProjInfo projInfo, List<String> languages) {
        try {
            asyncService.doSomethings(() -> {
                ProjFitnessWorkoutGenerateServiceImpl bean = SpringUtil.getBean(ProjFitnessWorkoutGenerateServiceImpl.class);
                //query relation
                List<Integer> workoutIds = workouts.stream().map(ProjFitnessWorkoutGenerate::getId).collect(Collectors.toList());
                List<ProjFitnessWorkoutGenerateExerciseVideo> relationVideos = generateVideoService.list(new LambdaQueryWrapper<ProjFitnessWorkoutGenerateExerciseVideo>()
                        .in(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessWorkoutGenerateId, workoutIds));
                Map<Integer, List<ProjFitnessWorkoutGenerateExerciseVideo>> relationMap = relationVideos.stream()
                        .collect(Collectors.groupingBy(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessWorkoutGenerateId));
                //query videos
                List<Integer> videoIds = relationVideos.stream().map(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessExerciseVideoId).collect(Collectors.toList());
                List<ProjFitnessExerciseVideo> videos = videoService.list(new LambdaQueryWrapper<ProjFitnessExerciseVideo>()
                        .in(ProjFitnessExerciseVideo::getId, videoIds));
                Map<Integer, ProjFitnessExerciseVideo> videoMap = videos.stream().collect(
                        Collectors.toMap(ProjFitnessExerciseVideo::getId, v -> v));
                //query group
                List<Integer> groupIds = relationVideos.stream().map(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessTemplateExerciseGroupId).collect(Collectors.toList());
                List<ProjFitnessTemplateExerciseGroup> groups = exerciseGroupService.list(new LambdaQueryWrapper<ProjFitnessTemplateExerciseGroup>()
                        .in(ProjFitnessTemplateExerciseGroup::getId, groupIds));
                Map<Integer, ProjFitnessTemplateExerciseGroup> groupMap = groups.stream().collect(
                        Collectors.toMap(ProjFitnessTemplateExerciseGroup::getId, g -> g));

                List<List<ProjFitnessWorkoutGenerate>> batches = ListUtil.split(workouts, 500);
                for (List<ProjFitnessWorkoutGenerate> batch : batches) {
                    try {
                        transactionTemplate.executeWithoutResult(status -> {
                            batchUpdateWorkout(userName, projInfo, bean, batch, relationMap, videoMap, groupMap, languages);
                        });
                    } catch (Exception e) {
                        log.error("Batch processing failed", e);
                        //update workout's fileStatus to 1-processing
                        updateWorkoutFileStatus(GlobalConstant.TWO, userName, batch.stream().map(ProjFitnessWorkoutGenerate::getId).collect(Collectors.toList()));
                    }
                }
            });
        } catch (Exception exception) {
            log.warn(exception.getMessage(), exception);
        }
    }

    private void batchUpdateWorkout(String userName, ProjInfo projInfo, ProjFitnessWorkoutGenerateServiceImpl bean,
                                    List<ProjFitnessWorkoutGenerate> batch,
                                    Map<Integer, List<ProjFitnessWorkoutGenerateExerciseVideo>> relationMap,
                                    Map<Integer, ProjFitnessExerciseVideo> videoMap,
                                    Map<Integer, ProjFitnessTemplateExerciseGroup> groupMap, List<String> selectLanguages) {
        for (ProjFitnessWorkoutGenerate workout : batch) {
            List<ProjFitnessWorkoutGenerateExerciseVideo> relationList = relationMap.get(workout.getId());
            if (CollUtil.isEmpty(relationList)) {
                continue;
            }
            List<BaseGenerateVideoBO> bos = assembleGenerateBOs(workout, videoMap, groupMap, relationList);
            //处理视频音频信息
            Set<String> languages = getLanguages(selectLanguages, projInfo);
            BaseWorkoutBO baseWorkoutBO = bean.generateBaseWorkout(bos, new ArrayList<>(languages), true, null, null);
            //更新workoutGenerate
            workout.setAudioJsonUrl(baseWorkoutBO.getAudioJsonUrl());
            workout.setDuration(baseWorkoutBO.getDuration());
            workout.setCalorie(baseWorkoutBO.getCalorie());
            workout.setVideoUrl(baseWorkoutBO.getVideo2532Url());
            workout.setAudioLanguages(CollUtil.join(languages, GlobalConstant.COMMA));
            workout.setFileStatus(GlobalConstant.ZERO);//返回了结果默认就是0-成功
            workout.setUpdateUser(userName);
            bean.updateById(workout);
            //更新国际化
            Map<String, String> i18nMap = MapUtil.defaultIfEmpty(baseWorkoutBO.getAudioI18nUrl(), new HashMap<>());
            List<ProjFitnessWorkoutGenerateI18n> i18nWorkoutList = i18nMap.entrySet().stream().map(entry -> {
                ProjFitnessWorkoutGenerateI18n workoutI18n = new ProjFitnessWorkoutGenerateI18n();
                workoutI18n.setProjFitnessWorkoutGenerateId(workout.getId());
                workoutI18n.setLanguage(entry.getKey());
                workoutI18n.setAudioJsonUrl(entry.getValue());
                workoutI18n.setProjId(projInfo.getId());
                workoutI18n.setCreateUser(userName);
                return workoutI18n;
            }).collect(Collectors.toList());
            //delete old i18nData
            LambdaUpdateWrapper<ProjFitnessWorkoutGenerateI18n> deleteWrapper = new LambdaUpdateWrapper<>();
            deleteWrapper.eq(ProjFitnessWorkoutGenerateI18n::getProjFitnessWorkoutGenerateId, workout.getId());
            deleteWrapper.eq(ProjFitnessWorkoutGenerateI18n::getProjId, projInfo.getId());
            deleteWrapper.eq(ProjFitnessWorkoutGenerateI18n::getDelFlag, 0);
            deleteWrapper.in(CollUtil.isNotEmpty(selectLanguages), ProjFitnessWorkoutGenerateI18n::getLanguage, selectLanguages);
            workoutGenerateI18nService.remove(deleteWrapper);
            if (CollUtil.isNotEmpty(i18nWorkoutList)) workoutGenerateI18nService.saveBatch(i18nWorkoutList);
            //填充workoutGenerateExerciseVideo
            Map<Integer, BaseGenerateVideoBO> baseGenerateVideoIdMap = bos.stream().collect(
                    Collectors.toMap(b -> b.getVideo().getId(), Function.identity()));
            relationList.forEach(r -> {
                BaseGenerateVideoBO bo = baseGenerateVideoIdMap.get(r.getProjFitnessExerciseVideoId());
                r.setPreviewDuration(bo.getPreviewDuration());
                r.setVideoDuration(bo.getVideoDuration());
                r.setUpdateUser(userName);
            });
            generateVideoService.updateBatchById(relationList);
        }
    }

    private List<BaseGenerateVideoBO> assembleGenerateBOs(ProjFitnessWorkoutGenerate workout,
                                                          Map<Integer, ProjFitnessExerciseVideo> relationVideoList,
                                                          Map<Integer, ProjFitnessTemplateExerciseGroup> groupMap,
                                                          List<ProjFitnessWorkoutGenerateExerciseVideo> relationList) {
        List<BaseGenerateVideoBO> generateVideoBOS = new ArrayList<>(relationList.size());
        relationList.forEach(item -> {
            ProjFitnessTemplateExerciseGroup group = groupMap.get(item.getProjFitnessTemplateExerciseGroupId());
            BaseGenerateVideoBO generateVideoBO = new BaseGenerateVideoBO();
            generateVideoBO.setVideoRound(group.getRounds());
            generateVideoBO.setGroupId(group.getId());
            generateVideoBO.setVideo(relationVideoList.get(item.getProjFitnessExerciseVideoId()));
            generateVideoBOS.add(generateVideoBO);
        });
        return generateVideoBOS;
    }

    @Data
    private static class M3u8GenerateContext {
        private List<ProjFitnessWorkoutGenerate> workoutsDetails;
        private List<ProjFitnessWorkoutGenerateExerciseVideo> relationVideos;
        private Map<Integer, List<ProjFitnessWorkoutGenerateExerciseVideo>> relationMap;
        private Map<Integer, ProjFitnessExerciseVideo> videoMap;
        private Map<Integer, ProjFitnessTemplateExerciseGroup> groupMap;
        private Map<ManualExerciseTypeEnums, FitnessWorkoutGenerateSoundBO> exerciseSoundMap;
        private FitnessWorkoutGenerateVideoBO videoBO;
    }
}
