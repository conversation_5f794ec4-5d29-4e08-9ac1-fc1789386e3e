package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 整数范围查询对象
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@ApiModel("整数范围查询")
public class IntegerRangeQuery {

    @ApiModelProperty(value = "最小值（包含）")
    private Integer min;

    @ApiModelProperty(value = "最大值（包含）")
    private Integer max;

    /**
     * 检查范围是否有效
     */
    public boolean isValid() {
        if (min == null && max == null) {
            return false;
        }
        if (min != null && max != null) {
            return min <= max;
        }
        return true;
    }

    /**
     * 检查是否有查询条件
     */
    public boolean hasCondition() {
        return min != null || max != null;
    }

    /**
     * 获取范围描述
     */
    public String getRangeDescription() {
        if (min != null && max != null) {
            return min + "~" + max;
        } else if (min != null) {
            return ">=" + min;
        } else if (max != null) {
            return "<=" + max;
        }
        return "无限制";
    }
}
