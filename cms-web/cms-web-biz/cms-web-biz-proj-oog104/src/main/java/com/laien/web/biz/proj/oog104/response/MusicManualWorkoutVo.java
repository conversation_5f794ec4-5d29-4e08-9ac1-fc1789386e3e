package com.laien.web.biz.proj.oog104.response;

import com.laien.common.oog104.enums.manual.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "music 详情下返回手组 workout 部分信息", description = "music 详情下返回手组 workout 部分信息")
public class MusicManualWorkoutVo {

    @ApiModelProperty(value = "id")
    private Integer id;

    /**
     * Target Areas
     */
    @ApiModelProperty(value = "Target Areas")
    private List<ManualTargetEnums> target;

    /**
     * Difficulty
     */
    @ApiModelProperty(value = "Difficulty Level")
    private ManualDifficultyEnums difficulty;

    /**
     * Duration
     */
    @ApiModelProperty(value = "Duration in milliseconds")
    private Integer duration;

    /**
     * Calories Burned
     */
    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

}
