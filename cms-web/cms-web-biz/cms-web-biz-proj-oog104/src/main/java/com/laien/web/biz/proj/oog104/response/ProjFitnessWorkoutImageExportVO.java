/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog104.response;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>ProjFitnessWorkoutImage 导入对象 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
@Accessors(chain = true)
public class ProjFitnessWorkoutImageExportVO {

    @ExcelProperty(index = 0,value = "id")
    private Integer id;

    @ExcelProperty(index = 1,value = "name")
    private String name;

    @ExcelProperty(index = 2,value = "exclusive_type")
    private String exclusiveType;

    @ApiModelProperty("年龄段")
    @ExcelProperty(index = 3,value = "age_group")
    private String ageGroup;

    @ExcelProperty(index = 4,value = "target")
    private String target;

    @ExcelProperty(index = 5,value = "difficulty")
    private String difficulty;

    @ExcelProperty(index = 6,value = "intensity")
    private String intensity;

    @ExcelProperty(index = 7,value = "special_limit")
    private String specialLimit;

    @ExcelProperty(index = 8,value = "cover_image")
    private String coverImage;

    @ExcelProperty(index = 9,value = "detail_image")
    private String detailImage;

    @ExcelProperty(index = 10,value = "template_type")
    private String templateType;

    @ExcelProperty(index = 11,value = "workout_ids")
    private String workoutIds;


}