package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.oog104.bo.BaseGenerateVideoBO;
import com.laien.web.biz.proj.oog104.bo.BaseWorkoutBO;
import com.laien.web.biz.proj.oog104.bo.FitnessWorkoutGenerateSoundBO;
import com.laien.web.biz.proj.oog104.bo.FitnessWorkoutGenerateVideoBO;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessExerciseVideo;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplateTask;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutGenerate;
import com.laien.web.biz.proj.oog104.request.ProjFitnessManualWorkoutGenerateM3u8Req;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutGeneratePageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutGenerateUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoPageVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutGenerateDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutGeneratePageVO;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * proj_fitness_workout_generate 服务接口
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
public interface IProjFitnessWorkoutGenerateService extends IService<ProjFitnessWorkoutGenerate> {

    void generateWorkoutByTask(List<ProjFitnessTemplateTask> templateTasks, ProjInfo projInfo);

    /**
     *
     * @param workoutAndVideoList
     * @param languageList
     * @param soundBO 可为空
     * @return
     */
    BaseWorkoutBO generateBaseWorkout(List<BaseGenerateVideoBO> workoutAndVideoList, List<String> languageList, boolean m3u8Upload,
                                      FitnessWorkoutGenerateVideoBO generateVideoBO, FitnessWorkoutGenerateSoundBO soundBO);

    /**
     * 分页查询WorkoutGenerate列表
     *
     * @param req 查询条件
     * @return IPage<ProjFitnessWorkoutGeneratePageRes>
     */
    PageRes<ProjFitnessWorkoutGeneratePageVO> page(ProjFitnessWorkoutGeneratePageReq req);

    /**
     * 查询WorkoutGenerate详情
     * @param id WorkoutGenerate ID
     * @return
     */
    ProjFitnessWorkoutGenerateDetailVO findDetailById(Integer id);

    void update(ProjFitnessWorkoutGenerateUpdateReq workoutUpdateReq, Integer projId);

    /**
     * 分页查询健身视频列表
     *
     * @param pageReq 分页查询条件
     * @param id WorkoutGenerate ID
     * @return 分页结果
     */
    PageRes<ProjFitnessExerciseVideoPageVO> pageVideo(PageReq pageReq, Integer id);

    /**
     * 批量启用WorkoutGenerate
     *
     * @param idList 需要启用的ID列表
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用WorkoutGenerate
     *
     * @param idList 需要禁用的ID列表
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * 批量删除WorkoutGenerate
     *
     * @param idList 需要删除的ID列表
     */
    void deleteByIdList(List<Integer> idList);

    /**
     * 根据模板ID和任务ID查询WorkoutGenerate
     *
     * @param templateId 模板ID
     * @param taskId 任务ID
     * @return ProjFitnessWorkoutGenerate
     */
    ProjFitnessWorkoutGenerate getByTemplateIdAndTaskId(Integer templateId, Integer taskId);

    /**
     * 保存WorkoutGenerate
     *
     * @param workoutGenerate workoutGenerate
     * @return 保存后的ID
     */
    Integer saveWorkoutGenerate(ProjFitnessWorkoutGenerate workoutGenerate);

    /**
     * 更新WorkoutGenerate
     *
     * @param workoutGenerate workoutGenerate
     */
    void updateWorkoutGenerate(ProjFitnessWorkoutGenerate workoutGenerate);

    /**
     * 更新文件状态
     *
     * @param id ID
     * @param fileStatus 文件状态
     * @param failMessage 失败信息
     */
    void updateFileStatus(Integer id, Integer fileStatus, String failMessage);

    void generateM3u8Interrupt();

    /**
     * 生成m3u8异步
     * @param m3u8Req
     * @return
     */
    Integer generateM3u8ByQuery(ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req);

    /**
     * 生成m3u8
     * @param m3u8Req m3u8Req
     * @return
     */
    Boolean generateM3u8ByPageReq(ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req);

    /**
     * 生成m3u8
     * @param m3u8Req m3u8Req
     * @return
     */
    Boolean generateM3u8(ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req);

    /**
     * 计算target占比
     * @param workouts
     * @param workIdVideoMap
     */
    void calculateTargetRate(List<ProjFitnessWorkoutGenerate> workouts, Map<Integer, List<ProjFitnessExerciseVideo>> workIdVideoMap);
}
