package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 手动训练生成M3U8请求
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@ApiModel(value = "Manual Workout M3U8 Generation Request", description = "Request parameters for generating M3U8 files for manual workouts")
public class ProjFitnessManualWorkoutGenerateM3u8Req {

    /**
     * 训练ID列表
     */
    @ApiModelProperty(value = "List of Workout IDs to generate M3U8 files for")
    private List<Integer> workoutIds;

    /**
     * 语言列表
     */
    @ApiModelProperty(value = "List of languages to generate audio for")
    private List<String> languages;

    /**
     * 是否生成视频
     */
    @ApiModelProperty(value = "Whether to generate video files")
    private boolean videoFlag;

    /**
     * 是否生成音频
     */
    @ApiModelProperty(value = "Whether to generate audio files")
    private boolean audioFlag;

    @ApiModelProperty(value = "项目ID",hidden = true)
    private Integer projId;

    @ApiModelProperty(value = "分页参数")
    private ProjFitnessWorkoutGeneratePageReq pageReq;

    @ApiModelProperty(value = "分页参数",hidden = true)
    private String updateUser;
}
