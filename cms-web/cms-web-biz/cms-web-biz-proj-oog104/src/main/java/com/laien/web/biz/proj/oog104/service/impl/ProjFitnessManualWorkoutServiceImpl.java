package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.oog104.enums.manual.*;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.bo.BaseGenerateVideoBO;
import com.laien.web.biz.proj.oog104.bo.BaseWorkoutBO;
import com.laien.web.biz.proj.oog104.entity.*;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessManualWorkoutMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessManualWorkoutMapStruct;
import com.laien.web.biz.proj.oog104.request.*;
import com.laien.web.biz.proj.oog104.response.ProjFitnessManualWorkoutDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessManualWorkoutDetailVideoVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessManualWorkoutPageVO;
import com.laien.web.biz.proj.oog104.service.*;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_fitness_manual_workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjFitnessManualWorkoutServiceImpl extends ServiceImpl<ProjFitnessManualWorkoutMapper, ProjFitnessManualWorkout> implements IProjFitnessManualWorkoutService {
    // 数据库字段名常量
    private static final String FIELD_CATEGORY = "category";
    private static final String FIELD_AGE_GROUP = "age_group";
    private static final String FIELD_TARGET = "target";
    private static final String FIELD_EQUIPMENT = "equipment";
    private static final String FIELD_SPECIAL_LIMITS = "special_limit";

    private final ProjFitnessManualWorkoutMapStruct mapStruct;
    private final IProjFitnessManualWorkoutExerciseVideoService projFitnessManualWorkoutExerciseVideoService;
    private final IProjFitnessManualWorkoutI18nService i18nService;
    private final IProjFitnessExerciseVideoService projFitnessExerciseVideoService;
    private final IProjFitnessWorkoutGenerateService generateService;
    private final IProjInfoService projInfoService;
    private final IProjLmsI18nService projLmsI18nService;


    @Override
    public PageRes<ProjFitnessManualWorkoutPageVO> selectWorkoutPage(ProjFitnessManualWorkoutPageReq pageReq) {
        Integer id = pageReq.getId();
        String name = pageReq.getName();
        Integer status = pageReq.getStatus();
        Integer fileStatus = pageReq.getFileStatus();
        List<ManualCategoryEnums> category = pageReq.getCategory();
        Integer subscription = pageReq.getSubscription();
        Integer projId = pageReq.getProjId();
        if (projId == null) {
            projId = RequestContextUtils.getProjectId();
            pageReq.setProjId(projId);
        }

        // 获取新增的查询条件
        List<ManualTargetEnums> target = pageReq.getTarget();
        ManualDifficultyEnums difficulty = pageReq.getDifficulty();
        List<ManualEquipmentEnums> equipment = pageReq.getEquipment();
        List<ExerciseVideoSpecialLimitEnums> specialLimit = pageReq.getSpecialLimit();
        ManualIntensityEnums intensity = pageReq.getIntensity();
        ManualWorkoutTypeEnums workoutType = pageReq.getWorkoutType();
        List<ManualAgeGroupEnums> ageGroup = pageReq.getAgeGroup();

        List<Integer> videoIdList = pageReq.getVideoIdList();
        Set<Integer> workoutIdSet = new HashSet<>();
         if (CollUtil.isNotEmpty(videoIdList)) {
            //查询关系表，获取workoutIds
            List<ProjFitnessManualWorkoutExerciseVideo> workoutExerciseVideos = projFitnessManualWorkoutExerciseVideoService.list(
                    new LambdaQueryWrapper<ProjFitnessManualWorkoutExerciseVideo>()
                            .in(ProjFitnessManualWorkoutExerciseVideo::getProjFitnessExerciseVideoId, videoIdList)
                            .eq(BaseModel::getDelFlag, GlobalConstant.NO)
                            .select(ProjFitnessManualWorkoutExerciseVideo::getProjFitnessManualWorkoutId));
            if (CollUtil.isEmpty(workoutExerciseVideos)) {
                return new PageRes<>(pageReq.getPageNum(), pageReq.getPageSize(), 0L, 0L, Collections.emptyList());
            }
            workoutIdSet.addAll(workoutExerciseVideos.stream().
                    map(ProjFitnessManualWorkoutExerciseVideo::getProjFitnessManualWorkoutId).collect(Collectors.toSet()));
        }

        // 区间查询条件
        Integer minDuration = pageReq.getMinDuration();
        Integer maxDuration = pageReq.getMaxDuration();
        BigDecimal minCalorie = pageReq.getMinCalorie();
        BigDecimal maxCalorie = pageReq.getMaxCalorie();

        LambdaQueryWrapper<ProjFitnessManualWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(id), ProjFitnessManualWorkout::getId, id)
                .eq(ProjFitnessManualWorkout::getProjId, projId)
                .in(CollUtil.isNotEmpty(workoutIdSet), ProjFitnessManualWorkout::getId, workoutIdSet)
                .like(StringUtils.isNotBlank(name), ProjFitnessManualWorkout::getName, name)
                .eq(Objects.nonNull(status), ProjFitnessManualWorkout::getStatus, status)
                .eq(Objects.nonNull(fileStatus), ProjFitnessManualWorkout::getFileStatus, fileStatus)
                .eq(Objects.nonNull(subscription), ProjFitnessManualWorkout::getSubscription, subscription)
                .eq(Objects.nonNull(workoutType), ProjFitnessManualWorkout::getWorkoutType, workoutType)
                .eq(Objects.nonNull(difficulty), ProjFitnessManualWorkout::getDifficulty, difficulty)
                .eq(Objects.nonNull(intensity), ProjFitnessManualWorkout::getIntensity, intensity)
                .ge(Objects.nonNull(minDuration), ProjFitnessManualWorkout::getDuration, minDuration)
                .le(Objects.nonNull(maxDuration), ProjFitnessManualWorkout::getDuration, maxDuration)
                .ge(Objects.nonNull(minCalorie), ProjFitnessManualWorkout::getCalorie, minCalorie)
                .le(Objects.nonNull(maxCalorie), ProjFitnessManualWorkout::getCalorie, maxCalorie);

        // List型枚举字段使用FIND_IN_SET查询
        multipleSearchContainsOne(wrapper, category, FIELD_CATEGORY);
        multipleSearchContainsOne(wrapper, ageGroup, FIELD_AGE_GROUP);
        multipleSearchContainsOne(wrapper, target, FIELD_TARGET);
        multipleSearchContainsOne(wrapper, equipment, FIELD_EQUIPMENT);
        multipleSearchContainsOne(wrapper, specialLimit, FIELD_SPECIAL_LIMITS);

        wrapper.orderByDesc(ProjFitnessManualWorkout::getId);
        IPage<ProjFitnessManualWorkout> page = this.page(new Page<>(pageReq.getPageNum(), pageReq.getPageSize()), wrapper);
        List<ProjFitnessManualWorkout> records = page.getRecords();
        if(CollUtil.isEmpty(records)){
            return new PageRes<>(pageReq.getPageNum(), pageReq.getPageSize(), 0L, 0L, Collections.emptyList());
        }

        // 各workout关联视频数量统计
        List<Integer> ids = records.stream().map(ProjFitnessManualWorkout::getId).collect(Collectors.toList());
        List<WorkoutVideoCount> countVideosByWorkout = this.baseMapper.countVideosByWorkoutIds(ids);
        Map<Integer, Integer> videoCountNumMap = CollectionUtils.isEmpty(countVideosByWorkout) ? new HashMap<>() :
                countVideosByWorkout.stream()
                        .collect(Collectors.groupingBy(WorkoutVideoCount::getWorkoutId,
                                Collectors.summingInt(WorkoutVideoCount::getVideoCount)));
        Map<Integer, Integer> videoCountDisabledNumMap = CollectionUtils.isEmpty(countVideosByWorkout) ? new HashMap<>() :
                countVideosByWorkout.stream()
                        .filter(c -> Objects.nonNull(c.getVideoStatus()) && c.getVideoStatus().equals(GlobalConstant.STATUS_DISABLE))
                        .collect(Collectors.groupingBy(WorkoutVideoCount::getWorkoutId,
                                Collectors.summingInt(WorkoutVideoCount::getVideoCount)));

        List<ProjFitnessManualWorkoutPageVO> list = records.stream().map(workout -> {
            ProjFitnessManualWorkoutPageVO pageVO = mapStruct.entityToPageVO(workout);
            pageVO.setVideoNum(videoCountNumMap.getOrDefault(workout.getId(), 0));
            pageVO.setVideoDisabledNum(videoCountDisabledNumMap.getOrDefault(workout.getId(), 0));
            return pageVO;
        }).collect(Collectors.toList());
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }

    /**
     * 多选查询处理 有交集为真
     *
     * @param wrapper wrapper
     * @param options options
     * @param tableField tableField
     */
    private void multipleSearchContainsOne(LambdaQueryWrapper<ProjFitnessManualWorkout> wrapper, List<? extends IEnumBase> options, String tableField) {
        if(CollUtil.isEmpty(options)){
            return;
        }
        wrapper.and(orWrapper ->
                options.forEach(option ->
                        orWrapper.or().apply("FIND_IN_SET({0},"+tableField+")", option.getCode())));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveWorkout(ProjFitnessManualWorkoutAddReq workoutReq) {
        // 校验
        Integer projId = workoutReq.getProjId();
        if (projId == null) {
            projId = RequestContextUtils.getProjectId();
            workoutReq.setProjId(projId);
        }
        this.check(workoutReq, null, projId);
        ProjFitnessManualWorkout workout = mapStruct.addReqToEntity(workoutReq);
        workout.setStatus(GlobalConstant.STATUS_DRAFT);
        workout.setProjId(projId);
        workout.setEventName("");
        if(CollUtil.isEmpty(workout.getSpecialLimit())){
            workout.setSpecialLimit(CollUtil.newArrayList(ExerciseVideoSpecialLimitEnums.NONE));
        }
        this.save(workout);
        // 保存新的video关系
        List<ProjFitnessManualWorkoutExerciseVideo> relationVideos = this.saveRelation(workout.getId(), projId, workoutReq.getVideoList());
        //获取id，生成eventName
        Integer id = workout.getId();
        workout.setEventName(id+"+"+workout.getName());
        // 处理音视频
        processM3u8Info(workout, relationVideos,projId, null);
        this.updateById(workout);

        projLmsI18nService.handleI18n(ListUtil.of( workout), projId);
    }

    private void processM3u8Info(ProjFitnessManualWorkout workout, List<ProjFitnessManualWorkoutExerciseVideo> relationVideos,
                                 Integer projId, List<String> selectLanguages) {
        Set<String> languages = new HashSet<>();
        if (CollUtil.isEmpty(selectLanguages)) {
            languages.add(GlobalConstant.DEFAULT_LANGUAGE);
            ProjInfo projInfo = projInfoService.getById(projId);
            languages.addAll(StrUtil.split(projInfo.getLanguages(), GlobalConstant.COMMA, true, true));
        } else {
            languages.addAll(selectLanguages);
        }
        List<BaseGenerateVideoBO> generateVideoBOS = new ArrayList<>(relationVideos.size());
        Map<Integer,ProjFitnessExerciseVideo> videoMap = projFitnessExerciseVideoService.listByIds(
                relationVideos.stream().map(ProjFitnessManualWorkoutExerciseVideo::getProjFitnessExerciseVideoId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(ProjFitnessExerciseVideo::getId, Function.identity()));
        for (ProjFitnessManualWorkoutExerciseVideo item : relationVideos) {
            BaseGenerateVideoBO generateVideoBO = new BaseGenerateVideoBO();
            generateVideoBO.setVideoRound(item.getExerciseCircuit());
            generateVideoBO.setVideo(videoMap.get(item.getProjFitnessExerciseVideoId()));
            generateVideoBOS.add(generateVideoBO);
        }
        BaseWorkoutBO baseWorkoutBO = generateService.generateBaseWorkout(generateVideoBOS, new ArrayList<>(languages), true, null, null);
        workout.setVideoUrl(baseWorkoutBO.getVideo2532Url());
        workout.setAudioJsonUrl(baseWorkoutBO.getAudioJsonUrl());
        workout.setDuration(baseWorkoutBO.getDuration());
        workout.setCalorie(baseWorkoutBO.getCalorie());
        workout.setFileStatus(GlobalConstant.ZERO);//返回了结果默认就是1-成功
        workout.setAudioLanguages(CollUtil.join(languages, GlobalConstant.COMMA));

        //update relationDuration
        Map<Integer,BaseGenerateVideoBO> relationDurationMap = generateVideoBOS.stream().collect(
                Collectors.toMap(b->b.getVideo().getId(), Function.identity()));
        for (ProjFitnessManualWorkoutExerciseVideo item : relationVideos) {
            BaseGenerateVideoBO bo = relationDurationMap.get(item.getProjFitnessExerciseVideoId());
            if(Objects.nonNull(bo)){
                item.setVideoDuration(bo.getVideoDuration());
                item.setPreviewDuration(bo.getPreviewDuration());
            }
        }
        projFitnessManualWorkoutExerciseVideoService.updateBatchById(relationVideos);

        Map<String, String> i18nMap = MapUtil.defaultIfEmpty(baseWorkoutBO.getAudioI18nUrl(), new HashMap<>());
        List<ProjFitnessManualWorkoutI18n> i18nWorkoutList = i18nMap.entrySet().stream().map(entry -> {
            ProjFitnessManualWorkoutI18n workoutI18n = new ProjFitnessManualWorkoutI18n();
            workoutI18n.setProjFitnessManualWorkoutId(workout.getId());
            workoutI18n.setLanguage(entry.getKey());
            workoutI18n.setAudioJsonUrl(entry.getValue());
            workoutI18n.setProjId(projId);
            return  workoutI18n;
        }).collect(Collectors.toList());
        //delete old i18nData
        LambdaUpdateWrapper<ProjFitnessManualWorkoutI18n> deleteWrapper = new LambdaUpdateWrapper<>();
        deleteWrapper.eq(ProjFitnessManualWorkoutI18n::getProjFitnessManualWorkoutId, workout.getId());
        deleteWrapper.eq(ProjFitnessManualWorkoutI18n::getProjId, projId);
        deleteWrapper.eq(ProjFitnessManualWorkoutI18n::getDelFlag, 0);
        deleteWrapper.in(CollUtil.isNotEmpty(selectLanguages), ProjFitnessManualWorkoutI18n::getLanguage, selectLanguages);
        i18nService.remove(deleteWrapper);
        if (CollUtil.isNotEmpty(i18nWorkoutList)) i18nService.saveBatch(i18nWorkoutList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateWorkout(ProjFitnessManualWorkoutUpdateReq workoutReq) {
        Integer id = workoutReq.getId();
        ProjFitnessManualWorkout workoutFind = this.getById(id);
        if (Objects.isNull(workoutFind)) {
            throw new BizException("Data not found");
        }
        Integer projId = workoutFind.getProjId();
        // 校验
        this.check(workoutReq, id, projId);
        ProjFitnessManualWorkout workout = mapStruct.addReqToEntity(workoutReq);
        workout.setId(id);
        // 删除video关系
        this.deleteRelation(id);
        // 保存新的video关系
        List<ProjFitnessManualWorkoutExerciseVideo> relationVideos = this.saveRelation(id, projId, workoutReq.getVideoList());
        // 处理音视频
        processM3u8Info(workout, relationVideos, projId, null);
        if(CollUtil.isEmpty(workout.getSpecialLimit())){
            workout.setSpecialLimit(CollUtil.newArrayList(ExerciseVideoSpecialLimitEnums.NONE));
        }
        this.updateById(workout);

        projLmsI18nService.handleI18n(ListUtil.of( workout), projId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean generateM3u8(ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req) {
        //query workouts
        List<ProjFitnessManualWorkout> workouts = this.list(new LambdaQueryWrapper<ProjFitnessManualWorkout>()
                .in(ProjFitnessManualWorkout::getId, m3u8Req.getWorkoutIds()));
        if(CollectionUtils.isEmpty(workouts)){
            return null;
        }
        //query relation
        List<Integer> workoutIds = workouts.stream().map(ProjFitnessManualWorkout::getId).collect(Collectors.toList());
        List<ProjFitnessManualWorkoutExerciseVideo> relationVideos = projFitnessManualWorkoutExerciseVideoService.list(new LambdaQueryWrapper<ProjFitnessManualWorkoutExerciseVideo>()
               .in(ProjFitnessManualWorkoutExerciseVideo::getProjFitnessManualWorkoutId, workoutIds).orderByAsc(ProjFitnessManualWorkoutExerciseVideo::getId));
        Map<Integer, List<ProjFitnessManualWorkoutExerciseVideo>> relationMap = relationVideos.stream()
                .collect(Collectors.groupingBy(ProjFitnessManualWorkoutExerciseVideo::getProjFitnessManualWorkoutId));
        workouts.forEach(workout -> {
            List<ProjFitnessManualWorkoutExerciseVideo> videos = relationMap.get(workout.getId());
            this.processM3u8Info(workout,videos, m3u8Req.getProjId(),m3u8Req.getLanguages());
        });
        this.updateBatchById(workouts);
        return true;
    }

    /**
     * 校验
     *
     * @param workoutReq workoutReq
     * @param id id
     * @param projId 项目id
     */
    private void check(ProjFitnessManualWorkoutAddReq workoutReq, Integer id, Integer projId) {
        String name = workoutReq.getName();
        LambdaQueryWrapper<ProjFitnessManualWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessManualWorkout::getName, name)
                .ne(Objects.nonNull(id), ProjFitnessManualWorkout::getId, id)
                .eq(ProjFitnessManualWorkout::getProjId, projId);
        int count = this.count(wrapper);
        if (count > 0) {
            throw new BizException("name already exists");
        }
    }

    /**
     * 保存workout 关系
     *
     * @param id        id
     * @param projId    projId
     * @param videoList videoList
     * @return
     */
    private List<ProjFitnessManualWorkoutExerciseVideo> saveRelation(Integer id, Integer projId, List<ProjFitnessManualWorkoutAddVideoReq> videoList) {
        List<ProjFitnessManualWorkoutExerciseVideo> fitnessVideoList = new ArrayList<>();
        for (ProjFitnessManualWorkoutAddVideoReq videoReq : videoList) {
            ProjFitnessManualWorkoutExerciseVideo fitnessVideo = new ProjFitnessManualWorkoutExerciseVideo();
            fitnessVideo.setProjFitnessManualWorkoutId(id);
            fitnessVideo.setProjFitnessExerciseVideoId(videoReq.getId());
            fitnessVideo.setExerciseCircuit(videoReq.getExerciseCircuit());
            fitnessVideo.setUnitName(videoReq.getUnitName());
            fitnessVideoList.add(fitnessVideo);
        }
        projFitnessManualWorkoutExerciseVideoService.saveBatch(fitnessVideoList);
        return fitnessVideoList;
    }

    /**
     * 删除workout 关系
     *
     * @param id id
     */
    private void deleteRelation(Integer id) {
        projFitnessManualWorkoutExerciseVideoService.update(new ProjFitnessManualWorkoutExerciseVideo(),
                new LambdaUpdateWrapper<ProjFitnessManualWorkoutExerciseVideo>()
                        .set(ProjFitnessManualWorkoutExerciseVideo::getDelFlag, GlobalConstant.YES)
                        .eq(ProjFitnessManualWorkoutExerciseVideo::getProjFitnessManualWorkoutId, id)
        );
    }

    @Override
    public ProjFitnessManualWorkoutDetailVO getWorkoutDetail(Integer id) {
        ProjFitnessManualWorkout workoutFind = this.getById(id);
        if (Objects.isNull(workoutFind)) {
            throw new BizException("Data not found");
        }
        ProjFitnessManualWorkoutDetailVO detailVO = mapStruct.entityToDetailVO(workoutFind);

        List<ProjFitnessManualWorkoutDetailVideoVO> videoList = this.selectVideoList(id);
        detailVO.setVideoList(videoList);
        return detailVO;
    }

    /**
     * 获取workout video list
     *
     * @param id id
     * @return list
     */
    private List<ProjFitnessManualWorkoutDetailVideoVO> selectVideoList(Integer id) {
        List<ProjFitnessManualWorkoutExerciseVideo> fitnessWorkoutProjFitnessVideoList =
                projFitnessManualWorkoutExerciseVideoService.list(new LambdaQueryWrapper<ProjFitnessManualWorkoutExerciseVideo>()
                .eq(ProjFitnessManualWorkoutExerciseVideo::getProjFitnessManualWorkoutId, id));

        List<Integer> videoIds = fitnessWorkoutProjFitnessVideoList.stream()
                .map(ProjFitnessManualWorkoutExerciseVideo::getProjFitnessExerciseVideoId)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, ProjFitnessExerciseVideo> fitnessVideoListMap = projFitnessExerciseVideoService.listByIds(videoIds).stream()
                .collect(Collectors.toMap(ProjFitnessExerciseVideo::getId, t -> t));

        List<ProjFitnessManualWorkoutDetailVideoVO> videoList = new ArrayList<>();
        for (ProjFitnessManualWorkoutExerciseVideo fitnessWorkoutProjFitnessVideo : fitnessWorkoutProjFitnessVideoList) {
            ProjFitnessExerciseVideo video = fitnessVideoListMap.get(fitnessWorkoutProjFitnessVideo.getProjFitnessExerciseVideoId());
            if (video != null) {
                ProjFitnessManualWorkoutDetailVideoVO videoVO = mapStruct.entityToDetailVideoVO(video);
                videoVO.setExerciseCircuit(fitnessWorkoutProjFitnessVideo.getExerciseCircuit());
                videoVO.setUnitName(fitnessWorkoutProjFitnessVideo.getUnitName());
                videoList.add(videoVO );
            }
        }
        return videoList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        this.update(new ProjFitnessManualWorkout(), new LambdaUpdateWrapper<ProjFitnessManualWorkout>()
                .set(ProjFitnessManualWorkout::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessManualWorkout::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE)
                .in(ProjFitnessManualWorkout::getId, idList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        this.update(new ProjFitnessManualWorkout(), new LambdaUpdateWrapper<ProjFitnessManualWorkout>()
                .set(ProjFitnessManualWorkout::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjFitnessManualWorkout::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessManualWorkout::getId, idList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        for (Integer id : idList) {
            boolean flag = this.update(new ProjFitnessManualWorkout(), new LambdaUpdateWrapper<ProjFitnessManualWorkout>()
                    .set(ProjFitnessManualWorkout::getDelFlag, GlobalConstant.YES)
                    .eq(ProjFitnessManualWorkout::getStatus, GlobalConstant.STATUS_DRAFT)
                    .eq(ProjFitnessManualWorkout::getId, id));
            // 删除关系
            if (flag) {
                this.deleteRelation(id);
            }
        }
    }
}
