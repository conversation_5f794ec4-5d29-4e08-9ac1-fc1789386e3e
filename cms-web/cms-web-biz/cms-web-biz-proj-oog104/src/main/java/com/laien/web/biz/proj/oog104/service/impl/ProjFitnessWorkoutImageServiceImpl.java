package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.core.enums.util.EnumBaseUtils;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateIntensityEnums;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.core.util.AssertUtil;
import com.laien.web.biz.proj.core.util.SqlSnippetUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessManualWorkout;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutImage;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessWorkoutImageMapper;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutImageAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutImageImportReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutImageListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutImageUpdateReq;
import com.laien.web.biz.proj.oog104.response.MusicManualWorkoutVo;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutImageExportVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutImageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessManualWorkoutService;
import com.laien.web.biz.proj.oog104.service.ProjFitnessWorkoutImageService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 针对表【proj_fitness_workout_image(proj_fitness_workout_image)】的数据库操作Service实现
 * @createDate 2025-03-17 17:33:55
 */
@Service
public class ProjFitnessWorkoutImageServiceImpl extends ServiceImpl<ProjFitnessWorkoutImageMapper, ProjFitnessWorkoutImage>
        implements ProjFitnessWorkoutImageService {

    @Resource
    private Validator validator;
    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Resource
    private IProjFitnessManualWorkoutService projFitnessManualWorkoutService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void add(ProjFitnessWorkoutImageAddReq req) {

        LambdaQueryWrapper<ProjFitnessWorkoutImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessWorkoutImage::getTarget, req.getTarget());
        queryWrapper.eq(ProjFitnessWorkoutImage::getName, req.getName());
        //v3.8.0新增 template type 字段,三者确定唯一
        queryWrapper.eq(ProjFitnessWorkoutImage::getTemplateType, req.getTemplateType());

        // 不能存在target和name相同的记录
        AssertUtil.isTrue(this.count(queryWrapper) == 0, "name and target cannot be duplicated.");
        // 数据转换
        ProjFitnessWorkoutImage workoutImage = BeanUtil.toBean(req, ProjFitnessWorkoutImage.class, CopyOptions.create().setIgnoreNullValue(true));
        if(Objects.nonNull(req.getWorkoutIds())) {
            workoutImage.setWorkoutIds(req.getWorkoutIds().stream()
                    .map(String::valueOf)  // 将每个 Integer 转为 String
                    .collect(Collectors.joining(",")));
        }
        workoutImage.setStatus(GlobalConstant.STATUS_DRAFT);
        // 新增数据要排到最前面，先保存，再全量更新排序
        workoutImage.setSortNo(1);
        // 数据库中其他数据的排序字段全部加1，然后把新增数据的排序字段设置为1
        LambdaUpdateWrapper<ProjFitnessWorkoutImage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.setSql("sort_no = sort_no + 1");
        updateWrapper.isNotNull(ProjFitnessWorkoutImage::getSortNo);
        this.update(updateWrapper);
        this.save(workoutImage);

        projLmsI18nService.handleI18n(ListUtil.of(workoutImage), workoutImage.getProjId());
    }

    @Override
    public void updateImg(ProjFitnessWorkoutImageUpdateReq req) {
        ProjFitnessWorkoutImage workoutImage = this.getById(req.getId());
        AssertUtil.notNull(workoutImage, "workoutImage not exist");

        LambdaQueryWrapper<ProjFitnessWorkoutImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessWorkoutImage::getTarget, req.getTarget());
        queryWrapper.eq(ProjFitnessWorkoutImage::getName, req.getName());
        //v3.8.0新增 template type 字段,三者确定唯一
        queryWrapper.eq(ProjFitnessWorkoutImage::getTemplateType, req.getTemplateType());

        queryWrapper.ne(ProjFitnessWorkoutImage::getId, req.getId());
        // 不能存在target和name相同的记录
        AssertUtil.isTrue(this.count(queryWrapper) == 0, "name and target cannot be duplicated.");
        String ids = null;
        if (Objects.nonNull(req.getWorkoutIds())) {
            ids = req.getWorkoutIds().stream()
                    .map(String::valueOf)  // 将每个 Integer 转为 String
                    .collect(Collectors.joining(","));
        }
        // 数据转换
        LambdaUpdateWrapper<ProjFitnessWorkoutImage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BaseModel::getId, req.getId());
        updateWrapper.set(ProjFitnessWorkoutImage::getName,req.getName());
        updateWrapper.set(ProjFitnessWorkoutImage::getExclusiveType,req.getExclusiveType());
        updateWrapper.set(ProjFitnessWorkoutImage::getTemplateType,req.getTemplateType());
        String ageGroup = Optional.ofNullable(req.getAgeGroup()).map(list -> list.stream().map(age -> String.valueOf(age.getCode())).collect(Collectors.joining(","))).orElse(null);
        updateWrapper.set(ProjFitnessWorkoutImage::getAgeGroup,ageGroup);
        updateWrapper.set(ProjFitnessWorkoutImage::getTarget,req.getTarget());
        updateWrapper.set(ProjFitnessWorkoutImage::getDifficulty,req.getDifficulty());
        updateWrapper.set(ProjFitnessWorkoutImage::getIntensity,req.getIntensity());
        String specialLimit = Optional.ofNullable(req.getSpecialLimit()).map(list -> list.stream().map(specialLimitEnums -> String.valueOf(specialLimitEnums.getCode())).collect(Collectors.joining(","))).orElse(null);
        updateWrapper.set(ProjFitnessWorkoutImage::getSpecialLimit,specialLimit);
        updateWrapper.set(ProjFitnessWorkoutImage::getCoverImage, req.getCoverImage());
        updateWrapper.set(ProjFitnessWorkoutImage::getDetailImage, req.getDetailImage());
        updateWrapper.set(ProjFitnessWorkoutImage::getWorkoutIds, ids);

        this.update(updateWrapper);

        projLmsI18nService.handleI18n(ListUtil.of(this.getById(req.getId())), workoutImage.getProjId());
    }

    @Override
    public void del(List<Integer> ids) {

        this.baseMapper.selectBatchIds(ids).forEach(workoutImage -> {
            AssertUtil.notNull(workoutImage, "workoutImage not exist");
            AssertUtil.isFalse(Objects.equals(workoutImage.getStatus(), GlobalConstant.STATUS_ENABLE), "workoutImage status is enable");
        });
        this.removeByIds(ids);
    }

    @Override
    public void updateEnable(List<Integer> idList, boolean isEnable) {

        LambdaUpdateWrapper<ProjFitnessWorkoutImage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProjFitnessWorkoutImage::getId, idList);
        updateWrapper.set(ProjFitnessWorkoutImage::getStatus, isEnable ? GlobalConstant.STATUS_ENABLE : GlobalConstant.STATUS_DISABLE);
        updateWrapper.set(ProjFitnessWorkoutImage::getUpdateUser, RequestContextUtils.getLoginUserName());
        updateWrapper.set(ProjFitnessWorkoutImage::getUpdateTime, LocalDateTime.now());
        this.update(updateWrapper);
    }

    @Override
    public List<ProjFitnessWorkoutImageVO> list(ProjFitnessWorkoutImageListReq req) {

        LambdaQueryWrapper<ProjFitnessWorkoutImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(req.getId()), ProjFitnessWorkoutImage::getId, req.getId());
        queryWrapper.like(StringUtils.isNotBlank(req.getName()), ProjFitnessWorkoutImage::getName, req.getName());
        queryWrapper.eq(Objects.nonNull(req.getExclusiveType()), ProjFitnessWorkoutImage::getExclusiveType, req.getExclusiveType());
        queryWrapper.eq(Objects.nonNull(req.getTarget()), ProjFitnessWorkoutImage::getTarget, req.getTarget());
        queryWrapper.eq(Objects.nonNull(req.getStatus()), ProjFitnessWorkoutImage::getStatus, req.getStatus());
        queryWrapper.eq(Objects.nonNull(req.getDifficulty()), ProjFitnessWorkoutImage::getDifficulty, req.getDifficulty());
        queryWrapper.eq(Objects.nonNull(req.getIntensity()), ProjFitnessWorkoutImage::getIntensity, req.getIntensity());
        //v3.8.0新增 template type 字段
        queryWrapper.eq(Objects.nonNull(req.getTemplateType()), ProjFitnessWorkoutImage::getTemplateType, req.getTemplateType());
        //v8.4.0新增 workout  id 查询
        if(Objects.nonNull(req.getWorkoutId())) {
            queryWrapper.apply("FIND_IN_SET({0},workout_ids) > 0",req.getWorkoutId());
        }
        // 处理多选
        if (CollUtil.isNotEmpty(req.getAgeGroup())) {
            String ageGroups = SqlSnippetUtil.generateFindInSetAnd(req.getAgeGroup(), "age_group");
            queryWrapper.and(o -> o.apply(ageGroups));
        }

        if (CollUtil.isNotEmpty(req.getSpecialLimit())) {
            String specialLimits = SqlSnippetUtil.generateFindInSetAnd(req.getSpecialLimit(), "special_limit");
            queryWrapper.and(o -> o.apply(specialLimits));
        }

        queryWrapper.orderByAsc(ProjFitnessWorkoutImage::getSortNo);

        return this.list(queryWrapper).stream().map(entity -> {
            ProjFitnessWorkoutImageVO vo = BeanUtil.toBean(entity, ProjFitnessWorkoutImageVO.class);
            if (entity.getWorkoutIds() != null) {
                List<Integer> workIdList = Arrays.stream(entity.getWorkoutIds().split(","))
                        .map(String::trim)  // 去除空格（如果有）
                        .filter(s -> !s.isEmpty())  // 过滤空字符串
                        .map(Integer::parseInt)  // 转为 Integer
                        .collect(Collectors.toList());
                vo.setWorkoutIds(workIdList);  // 假设 VO 类中有 workIdList 字段
            }
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ProjFitnessWorkoutImageExportVO> listExportVO(ProjFitnessWorkoutImageListReq req) {

        return this.list(req).stream().map(voCovert2ExportVO).collect(Collectors.toList());
    }

    private final Function<ProjFitnessWorkoutImageVO, ProjFitnessWorkoutImageExportVO> voCovert2ExportVO = vo -> {
        ProjFitnessWorkoutImageExportVO exportVO = BeanUtil.toBean(vo, ProjFitnessWorkoutImageExportVO.class, CopyOptions.create().setIgnoreProperties("exclusiveType", "templateType","ageGroup", "target", "difficulty", "intensity", "specialLimit"));
        // 这里处理枚举字段
        if (Objects.nonNull(vo.getExclusiveType())) {
            exportVO.setExclusiveType(vo.getExclusiveType().getName());
        }
        //v8.3.0新增 template type
        if (Objects.nonNull(vo.getTemplateType())) {
            exportVO.setTemplateType(vo.getTemplateType().getName());
        }
        if (CollUtil.isNotEmpty(vo.getAgeGroup())) {
            exportVO.setAgeGroup(vo.getAgeGroup().stream().map(EnumBaseUtils::getName).collect(Collectors.joining(GlobalConstant.COMMA)));
        }
        if (Objects.nonNull(vo.getTarget())) {
            exportVO.setTarget(vo.getTarget().getName());
        }
        if (Objects.nonNull(vo.getDifficulty())) {
            exportVO.setDifficulty(vo.getDifficulty().getName());
        }
        if (Objects.nonNull(vo.getIntensity())) {
            exportVO.setIntensity(vo.getIntensity().getName());
        }
        if (CollUtil.isNotEmpty(vo.getSpecialLimit())) {
            exportVO.setSpecialLimit(vo.getSpecialLimit().stream().map(EnumBaseUtils::getName).collect(Collectors.joining(GlobalConstant.COMMA)));
        }
        exportVO.setSpecialLimit(vo.getSpecialLimit().stream().map(EnumBaseUtils::getName).collect(Collectors.joining(GlobalConstant.COMMA)));
        return exportVO;
    };

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Throwable.class)
    public List<String> importExcel(Integer projId, MultipartFile excel) {

        List<ProjFitnessWorkoutImageImportReq> list = Lists.newArrayList();
        EasyExcel.read(excel.getInputStream(), ProjFitnessWorkoutImageImportReq.class, new AnalysisEventListener<ProjFitnessWorkoutImageImportReq>() {
            @Override
            public void invoke(ProjFitnessWorkoutImageImportReq req, AnalysisContext analysisContext) {
                list.add(req);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {

            }
        }).sheet(0).doRead();

        // 校验name和target不能重复,新增添加 template type 不重复
        LambdaQueryWrapper<ProjFitnessWorkoutImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BaseModel::getId, ProjFitnessWorkoutImage::getTarget, ProjFitnessWorkoutImage::getName, ProjFitnessWorkoutImage::getTemplateType);
        list.forEach(data ->
                queryWrapper.or(wrapper ->
                        wrapper.eq(ProjFitnessWorkoutImage::getName, data.getName())
                                .eq(ProjFitnessWorkoutImage::getTarget, EnumBaseUtils.findEnum(ManualTargetEnums::getName, data.getTarget()))
                                .eq(ProjFitnessWorkoutImage::getTemplateType, EnumBaseUtils.findEnum(TemplateTypeEnums::getName, data.getTemplateType()))
                )
        );
        // 将请求数据和数据库中必然重复的数据一次性查询出来做判断，避免在循环中查询数据库
        List<ProjFitnessWorkoutImage> nameAndTargetImages = this.baseMapper.selectList(queryWrapper);
        // 逐一校验
        List<String> messages = list.stream()
                .flatMap(req -> Stream.concat(
                        // 校验器基本校验
                        validator.validate(req).stream()
                                .map(v -> String.format("%s:%s", req.getName(), v.getMessage())),
                        // name和target与数据库数据重复
                        nameAndTargetImages.stream()
                                .filter(image ->
                                        req.getName().equals(image.getName())
                                                && Objects.equals(req.getTarget(), image.getTarget().getName())
                                                && Objects.equals(req.getTemplateType(), image.getTemplateType().getName())
                                                && !Objects.equals(req.getId(), image.getId()))
                                .limit(1).map(image -> String.format("%s:name and target,template type cannot be duplicated", req.getName()))
                ))
                .collect(Collectors.toList());
        messages.addAll(validateAgeGroupAndTarget(list));
        messages.addAll(validateNameAndTarget(list));
        if (!messages.isEmpty()) {
            return messages;
        }

        // 区分新增和修改
        Map<Boolean, List<ProjFitnessWorkoutImageImportReq>> groupByInsert = list.stream().collect(Collectors.groupingBy(req -> Objects.isNull(req.getId())));
        if (CollUtil.isNotEmpty(groupByInsert.get(true))) {
            // 先将其他数据的排序统一向后位移，然后再插入数据
            AtomicInteger sortNo = new AtomicInteger(groupByInsert.get(true).size());
            List<ProjFitnessWorkoutImage> forInsert = groupByInsert.get(true).stream().map(importReqCovert2Entity).peek(entity -> entity.setProjId(projId).setSortNo(sortNo.getAndDecrement())).collect(Collectors.toList());
            LambdaUpdateWrapper<ProjFitnessWorkoutImage> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.setSql("sort_no = sort_no + " + forInsert.size());
            updateWrapper.isNotNull(ProjFitnessWorkoutImage::getSortNo);
            this.update(updateWrapper);

            this.saveBatch(forInsert);

            projLmsI18nService.handleI18n(forInsert, projId);
        }
        if (CollUtil.isNotEmpty(groupByInsert.get(false))) {
            List<ProjFitnessWorkoutImage> insert = groupByInsert.get(false).stream().map(importReqCovert2Entity).collect(Collectors.toList());
            this.updateBatchById(insert);
            projLmsI18nService.handleI18n(insert, projId);
        }
        return messages;
    }

    private Collection<String> validateAgeGroupAndTarget(List<ProjFitnessWorkoutImageImportReq> reqList) {

        // 五种类型之外，age group 和 target 为 必填
        Set<String> canBeenNull = Stream.of(ExclusiveTypeEnums.DAILY_STRETCH, ExclusiveTypeEnums.DAILY_HIIT, ExclusiveTypeEnums.DAILY_ABS, ExclusiveTypeEnums.DAILY_BOOTY, ExclusiveTypeEnums.DAILY_FAT_LOSS)
                .map(ExclusiveTypeEnums::getName).collect(Collectors.toSet());
        List<String> result = new ArrayList<>();
        for (ProjFitnessWorkoutImageImportReq req : reqList) {
            if (canBeenNull.contains(req.getExclusiveType())) {
                continue;
            }
            if (StringUtils.isBlank(req.getAgeGroup())) {
                result.add(String.format("%s:age group cannot be empty", req.getName()));
            }
            if (StringUtils.isBlank(req.getTarget())) {
                result.add(String.format("%s:target cannot be empty", req.getName()));
            }
        }
        return result;

    }

    private List<String> validateNameAndTarget(List<ProjFitnessWorkoutImageImportReq> reqList) {
        List<String> result = new ArrayList<>();
        Set<String> processedPairs = new HashSet<>();
        for (ProjFitnessWorkoutImageImportReq req : reqList) {
            String pairKey = req.getName() + "|" + req.getTarget() + "|" + req.getTemplateType();
            if (processedPairs.contains(pairKey)) {
                result.add(String.format("%s:name and target,template type cannot be duplicated", req.getName()));
            } else {
                processedPairs.add(pairKey);
            }
        }

        return result;
    }

    private final Function<ProjFitnessWorkoutImageImportReq, ProjFitnessWorkoutImage> importReqCovert2Entity = req -> {
        ProjFitnessWorkoutImage image = BeanUtil.toBean(req, ProjFitnessWorkoutImage.class, CopyOptions.create().setIgnoreProperties("exclusiveType","templateType", "ageGroup", "target", "difficulty", "intensity", "specialLimit"));
        // 这里处理枚举字段
        image.setExclusiveType(EnumBaseUtils.findEnum(ExclusiveTypeEnums::getName, req.getExclusiveType()));
        image.setTemplateType(EnumBaseUtils.findEnum(TemplateTypeEnums::getName, req.getTemplateType()));
        image.setAgeGroup(EnumBaseUtils.findEnumList(ManualAgeGroupEnums::getName, req.getAgeGroup()));
        image.setTarget(EnumBaseUtils.findEnum(ManualTargetEnums::getName, req.getTarget()));
        image.setDifficulty(EnumBaseUtils.findEnum(ManualDifficultyEnums::getName, req.getDifficulty()));
        image.setIntensity(EnumBaseUtils.findEnum(TemplateIntensityEnums::getName, req.getIntensity()));
        image.setSpecialLimit(EnumBaseUtils.findEnumList(ExerciseVideoSpecialLimitEnums::getName, req.getSpecialLimit()));
        // 多选值有重复，这里默认去重，而不是报错
        image.setAgeGroup(CollUtil.distinct(image.getAgeGroup()));
        image.setSpecialLimit(CollUtil.distinct(image.getSpecialLimit()));

        return image;
    };

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateSort(List<Integer> idList) {

        // 按传入顺序指定排序
        AtomicInteger sortNo = new AtomicInteger(1);
        List<ProjFitnessWorkoutImage> batchImage = idList.stream().map(id -> {
            ProjFitnessWorkoutImage workoutImage = new ProjFitnessWorkoutImage();
            workoutImage.setId(id);
            workoutImage.setSortNo(sortNo.getAndIncrement());
            return workoutImage;
        }).collect(Collectors.toList());
        this.updateBatchById(batchImage);
    }

    @Override
    public ProjFitnessWorkoutImageVO detail(Integer id) {

        ProjFitnessWorkoutImage workoutImage = this.getById(id);
        AssertUtil.notNull(workoutImage, "workoutImage not exist");
        ProjFitnessWorkoutImageVO result = BeanUtil.toBean(workoutImage, ProjFitnessWorkoutImageVO.class);
        Optional.ofNullable(workoutImage.getWorkoutIds())
                .map(ids -> Arrays.stream(ids.split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()))
                .filter(CollUtil::isNotEmpty)
                .ifPresent(workoutIds -> {
                    result.setWorkoutIds(workoutIds);

                    List<ProjFitnessManualWorkout> workouts = projFitnessManualWorkoutService.list(
                            new LambdaQueryWrapper<ProjFitnessManualWorkout>()
                                    .in(ProjFitnessManualWorkout::getId, workoutIds)
                    );

                    List<MusicManualWorkoutVo> workoutVos = BeanUtil.copyToList(workouts, MusicManualWorkoutVo.class);
                    result.setWorkoutList(workoutVos);
                });

        return result;
    }

    public static void main(String[] args) {
        System.out.println(new Random().nextInt(2) + 1);
    }
}




