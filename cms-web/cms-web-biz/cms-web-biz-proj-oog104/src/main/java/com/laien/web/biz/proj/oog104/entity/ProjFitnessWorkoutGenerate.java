package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.common.oog104.enums.manual.*;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * proj_fitness_workout_generate
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjFitnessWorkoutGenerate对象", description = "proj_fitness_workout_generate")
@TableName(autoResultMap = true)
public class ProjFitnessWorkoutGenerate extends BaseModel {

    private static final long serialVersionUID = -3980786728453076657L;

    @ApiModelProperty(value = "proj_fitness_template_id")
    private Integer projFitnessTemplateId;

    @ApiModelProperty(value = "proj_fitness_template_task_id")
    private Integer projFitnessTemplateTaskId;

    @ApiModelProperty("表标识")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_WORKOUT_GENERATE;

    @ApiModelProperty(value = "target code")
    private ManualTargetEnums target;

    @ApiModelProperty(value = "difficulty code")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "equipment code")
    private ManualEquipmentEnums equipment;

    @ApiModelProperty(value = "特殊限制code (多选, 逗号分隔)")
    @TableField(typeHandler = ExerciseVideoSpecialLimitEnums.TypeHandler.class)
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "组成的Exercise的Intensity包含的合集 , 多个用英文逗号分隔")
    @TableField(typeHandler = ManualIntensityEnums.TypeHandler.class)
    private List<ManualIntensityEnums> intensity;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "video的m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;

    @ApiModelProperty(value = "已生成Audio的语言，多个逗号分隔")
    private String audioLanguages;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "生成m3u8文件的状态 运行中 0, 成功 1, 失败 2")
    private Integer fileStatus;

    @ApiModelProperty(value = "失败信息")
    private String failMessage;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "Workout Type")
    private ManualExerciseTypeEnums workoutType;

    @ApiModelProperty(value = "部位")
    private ManualPositionEnums position;

    @ApiModelProperty(value = "Main中Abs占Main的比例，存xx%中的xx为int")
    private Integer absRate;

    @ApiModelProperty(value = "Main中Standing占Main的比例，存xx%中的xx为int")
    private Integer standingRate;
}
