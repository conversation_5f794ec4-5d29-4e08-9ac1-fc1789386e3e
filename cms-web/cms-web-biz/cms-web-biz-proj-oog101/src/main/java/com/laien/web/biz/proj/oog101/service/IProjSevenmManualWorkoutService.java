package com.laien.web.biz.proj.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.web.biz.proj.oog101.bo.*;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmManualWorkout;
import com.laien.web.biz.proj.oog101.request.ProjSevenmManualWorkoutAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmManualWorkoutGenerateM3u8Req;
import com.laien.web.biz.proj.oog101.request.ProjSevenmManualWorkoutPageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmManualWorkoutUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmManualWorkoutDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmManualWorkoutPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * proj_sevenm_manual_workout 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
public interface IProjSevenmManualWorkoutService extends IService<ProjSevenmManualWorkout> {

    /**
     * 分页查询训练列表
     *
     * @param pageReq 分页查询参数
     * @return 分页结果
     */
    PageRes<ProjSevenmManualWorkoutPageVO> selectWorkoutPage(ProjSevenmManualWorkoutPageReq pageReq);

    /**
     * 保存训练
     *
     * @param workoutReq 训练信息
     */
    void saveWorkout(ProjSevenmManualWorkoutAddReq workoutReq);

    /**
     * 更新训练
     *
     * @param workoutReq 训练信息
     */
    void updateWorkout(ProjSevenmManualWorkoutUpdateReq workoutReq);


    Boolean generateM3u8(ProjSevenmManualWorkoutGenerateM3u8Req workoutReq);

    /**
     * 获取训练详情
     *
     * @param id 训练ID
     * @return 训练详情
     */
    ProjSevenmManualWorkoutDetailVO getWorkoutDetail(Integer id);

    /**
     * 启用训练
     *
     * @param idList 训练ID列表
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 禁用训练
     *
     * @param idList 训练ID列表
     */
    void updateDisableByIds(List<Integer> idList);


    /**
     * 获取多语言系统音
     */
    Map<String, SevenmSoundBO> getSoundI18n(List<String> languages, SevenmGenderEnums gender);

    void generateM3u8(GenerateSevenmWorkoutFileContextBO contextBO);


    void generateAudio(GenerateSevenmWorkoutFileContextBO contextBO);

    Map<String, Map<Integer, SevenmVideoSoundI18nBO>> listVideoI18n(List<String> languages, List<ProjSevenmExerciseVideo> videoList);

    /**
     * 删除训练
     *
     * @param idList 训练ID列表
     */
    void deleteByIds(List<Integer> idList);


    /**
     * 生成m3u8和audio json,并上传
     */
    void generateWorkoutFile(GenerateSevenmWorkoutFileContextBO contextBO);

}
