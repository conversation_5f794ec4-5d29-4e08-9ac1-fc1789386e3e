package com.laien.web.biz.proj.oog101.request;

import com.laien.common.oog101.enums.*;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 7M Manual Workout Page Request
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Manual Workout Page Request", description = "Request parameters for manual workout pagination query")
public class ProjSevenmManualWorkoutPageReq extends PageReq {

    /**
     * ID
     */
    @ApiModelProperty(value = "Workout ID")
    private Integer id;

    /**
     * Project ID
     */
    @ApiModelProperty(value = "Project ID")
    private Integer projId;

    /**
     * Name
     */
    @ApiModelProperty(value = "Workout Name")
    private String name;

    /**
     * Status
     */
    @ApiModelProperty(value = "Status (0-Draft, 1-Enabled, 2-Disabled)")
    private Integer status;

    /**
     * File Status
     */
    @ApiModelProperty(value = "File Status (0-Success, 1-Processing, 2-Failed)")
    private Integer fileStatus;

    /**
     * Categories
     */
    @ApiModelProperty(value = "Categories 多选")
    private List<SevenmWorkoutCategoryEnums> category;

    @ApiModelProperty(value = "Workout Type")
    private SevenmTemplateTypeEnums workoutType;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    /**
     * Subscription 0-No, 1-Yes
     */
    @ApiModelProperty(value = "Subscription (0-No, 1-Yes)")
    private Integer subscription;

    /**
     * Target Areas
     */
    @ApiModelProperty(value = "Target 多选")
    private List<SevenmTargetEnums> target;

    /**
     * Difficulty
     */
    @ApiModelProperty(value = "Difficulty Level")
    private SevenmDifficultyEnums difficulty;

    /**
     * Duration in milliseconds - Minimum
     */
    @ApiModelProperty(value = "Minimum Duration (in milliseconds)")
    private Integer minDuration;

    /**
     * Duration in milliseconds - Maximum
     */
    @ApiModelProperty(value = "Maximum Duration (in milliseconds)")
    private Integer maxDuration;

    /**
     * Calories Burned - Minimum
     */
    @ApiModelProperty(value = "Minimum Calories Burned")
    private BigDecimal minCalorie;

    /**
     * Calories Burned - Maximum
     */
    @ApiModelProperty(value = "Maximum Calories Burned")
    private BigDecimal maxCalorie;

    @ApiModelProperty(value = "Video ID List")
    private List<Integer> videoIdList;
}
