package com.laien.web.biz.proj.oog101.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog101.enums.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Manual Workout Add Request
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
public class ProjSevenmManualWorkoutAddReq implements Serializable {

    private static final long serialVersionUID = -1928688074430936123L;

    @ApiModelProperty(value = "Exercise Name")
    private String name;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "Cover Image, supports png/webp formats")
    private String coverImage;

    @ApiModelProperty(value = "Detail Image, supports png/webp formats")
    private String detailImage;

    @ApiModelProperty(value = "supplementImage, supports png/webp formats")
    private String supplementImage;

    @ApiModelProperty(value = "sketchImage, supports png/webp formats")
    private String sketchImage;

    @ApiModelProperty(value = "Workout Type")
    private SevenmTemplateTypeEnums workoutType;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "Target Areas")
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "Difficulty")
    private SevenmDifficultyEnums difficulty;

    @ApiModelProperty(value = "Categories")
    private List<SevenmWorkoutCategoryEnums> category;

    @ApiModelProperty(value = "Description, maximum 1000 characters")
    private String description;

    @ApiModelProperty(value = "Duration in milliseconds")
    private Integer duration;

    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period Start")
    private LocalDateTime newTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period End")
    private LocalDateTime newTimeEnd;

    @ApiModelProperty(value = "Subscription 0-No, 1-Yes")
    private Integer subscription;

    @ApiModelProperty(value = "Video List")
    private List<ProjSevenmManualWorkoutAddVideoReq> videoList;

}
