package com.laien.web.biz.proj.oog101.controller;

import com.laien.web.biz.proj.oog101.entity.ProjSevenmManualWorkout;
import com.laien.web.biz.proj.oog101.request.ProjSevenmManualWorkoutAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmManualWorkoutGenerateM3u8Req;
import com.laien.web.biz.proj.oog101.request.ProjSevenmManualWorkoutPageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmManualWorkoutUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmManualWorkoutDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmManualWorkoutPageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmManualWorkoutService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *     Manual Workout 控制器
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Api(tags = "项目管理:Manual Workout")
@RestController
@RequestMapping("/proj/sevenmManualWorkout")
public class ProjSevenmManualWorkoutController extends ResponseController {

    @Resource
    private IProjSevenmManualWorkoutService manualWorkoutService;

    @ApiOperation(value = "Manual Workout 分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjSevenmManualWorkoutPageVO>> page(ProjSevenmManualWorkoutPageReq pageReq) {
        pageReq.setProjId(RequestContextUtils.getProjectId());
        PageRes<ProjSevenmManualWorkoutPageVO> pageRes = manualWorkoutService.selectWorkoutPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "Manual Workout 增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjSevenmManualWorkoutAddReq workoutReq) {
        Integer projectId = RequestContextUtils.getProjectId();
        workoutReq.setProjId(projectId);
        manualWorkoutService.saveWorkout(workoutReq);
        return succ();
    }

    @ApiOperation(value = "Manual Workout 修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjSevenmManualWorkoutUpdateReq workoutReq) {
        manualWorkoutService.updateWorkout(workoutReq);
        return succ();
    }

    @ApiOperation(value = "Manual Workout 详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjSevenmManualWorkoutDetailVO> detail(@PathVariable Integer id) {
        ProjSevenmManualWorkoutDetailVO detailVO = manualWorkoutService.getWorkoutDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "Manual Workout 批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        manualWorkoutService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "Manual Workout 批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        manualWorkoutService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "Manual Workout 批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        manualWorkoutService.deleteByIds(idList);
        return succ();
    }

    @ApiOperation(value = "Manual Workout 生成M3U8文件")
    @PostMapping("/generateM3u8")
    public ResponseResult<Boolean> generateM3u8(@RequestBody ProjSevenmManualWorkoutGenerateM3u8Req m3u8Req) {
        m3u8Req.setProjId(RequestContextUtils.getProjectId());
        Boolean result = manualWorkoutService.generateM3u8(m3u8Req);
        return succ(result);
    }
}
