package com.laien.web.biz.proj.oog101.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/3/20 13:52
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class SevenmSoundBO {

    private AudioJson101BO first;

    private AudioJson101BO next;

    private AudioJson101BO last;

    private AudioJson101BO threeTwoOne;

    private AudioJson101BO go;

    private AudioJson101BO beeBeeBee;

    private List<AudioJson101BO> promptList = new ArrayList<>();

}
