package com.laien.web.biz.proj.oog101.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog101.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Manual Workout Detail Response
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@ApiModel(value = "Manual Workout Detail Response", description = "Detailed information of a manual workout")
public class ProjSevenmManualWorkoutDetailVO {

    /**
     * ID
     */
    @ApiModelProperty(value = "Workout ID")
    private Integer id;

    /**
     * Name
     */
    @ApiModelProperty(value = "Workout Name")
    private String name;

    /**
     * Event Name
     */
    @ApiModelProperty(value = "Event Name")
    private String eventName;

    /**
     * Cover Image
     */
    @ApiModelProperty(value = "Cover Image URL")
    private String coverImage;

    /**
     * Detail Image
     */
    @ApiModelProperty(value = "Detail Image URL")
    private String detailImage;

    @ApiModelProperty(value = "supplementImage, supports png/webp formats")
    private String supplementImage;

    @ApiModelProperty(value = "sketchImage, supports png/webp formats")
    private String sketchImage;

    @ApiModelProperty(value = "Workout Type")
    private SevenmTemplateTypeEnums workoutType;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    /**
     * Categories
     */
    @ApiModelProperty(value = "Categories")
    private List<SevenmWorkoutCategoryEnums> category;


    /**
     * Target Areas
     */
    @ApiModelProperty(value = "Target Areas")
    private List<SevenmTargetEnums> target;

    /**
     * Difficulty
     */
    @ApiModelProperty(value = "Difficulty Level")
    private SevenmDifficultyEnums difficulty;


    /**
     * Description
     */
    @ApiModelProperty(value = "Description")
    private String description;

    /**
     * Duration
     */
    @ApiModelProperty(value = "Duration in milliseconds")
    private Integer duration;

    /**
     * Calories Burned
     */
    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    /**
     * Time Period Start
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period Start")
    private LocalDateTime newTimeStart;

    /**
     * Time Period End
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period End")
    private LocalDateTime newTimeEnd;

    /**
     * Subscription 0-No, 1-Yes
     */
    @ApiModelProperty(value = "Subscription (0-No, 1-Yes)")
    private Integer subscription;

    /**
     * Status
     */
    @ApiModelProperty(value = "Status (0-Draft, 1-Enabled, 2-Disabled)")
    private Integer status;

    /**
     * File Status
     */
    @ApiModelProperty(value = "File Status (0-Processing, 1-Success, 2-Failed)")
    private Integer fileStatus;

    /**
     * Failure Message
     */
    @ApiModelProperty(value = "Failure Message")
    private String failMessage;

    /**
     * Audio Languages
     */
    @ApiModelProperty(value = "Audio Languages, comma separated")
    private String audioLanguages;

    /**
     * Video List
     */
    @ApiModelProperty(value = "List of Exercise Videos")
    private List<ProjSevenmManualWorkoutDetailVideoVO> videoList;
}
